<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relazione Tecnica ASDP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <style>
    :root {
        --primary-color: #FF7043;
        --bg-color: #1E1E1E;
        --text-color: #FFFFFF;
        --border-color: #333333;
        --hover-color: #FF8A65;
    }

            body {
                margin: 0;
                padding: 0;
        background-color: var(--bg-color);
        color: var(--text-color);
        font-family: 'Segoe UI', Arial, sans-serif;
        display: flex;
    }

    /* Sidebar per l'indice */
    .sidebar {
        width: 300px;
        height: 100vh;
        background: var(--bg-color);
        border-right: 1px solid var(--border-color);
        padding: 20px;
        position: fixed;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color) var(--bg-color);
    }

    .sidebar::-webkit-scrollbar {
        width: 8px;
    }

    .sidebar::-webkit-scrollbar-track {
        background: var(--bg-color);
    }

    .sidebar::-webkit-scrollbar-thumb {
        background-color: var(--primary-color);
        border-radius: 4px;
    }

    .sidebar-header {
        padding: 10px 0;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--primary-color);
        font-size: 1.2em;
        font-weight: bold;
    }

    .toc {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .toc li {
        margin: 8px 0;
        padding-left: 15px;
        border-left: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .toc li:hover {
        border-left-color: var(--primary-color);
    }

    .toc a {
        color: var(--text-color);
        text-decoration: none;
        transition: color 0.3s ease;
        display: block;
        padding: 5px 0;
    }

    .toc a:hover {
        color: var(--primary-color);
    }

    .toc a.active {
        color: var(--primary-color);
        font-weight: bold;
    }

    /* Contenuto principale */
    .main-content {
        margin-left: 300px;
        padding: 40px;
        flex: 1;
        background-color: #FFFFFF;
        color: #000000;
        min-height: 100vh;
    }

    .markdown-body {
        box-sizing: border-box;
        min-width: 200px;
        max-width: 980px;
        margin: 0 auto;
        padding: 45px;
    }

    /* Stili per le sezioni della documentazione */
    .section-header {
        margin-bottom: 2rem;
    }

    .section-header h1 {
        color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    .section-header p {
        color: #666;
        font-style: italic;
    }

    .section-content {
        margin-bottom: 3rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .doc-section {
        margin: 2rem 0;
        padding: 1.5rem;
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .doc-section h3 {
        color: var(--primary-color);
        margin-top: 0;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #eee;
    }

    /* Stili per il codice e i blocchi di codice */
    .code-block {
        background: #2d2d2d;
        color: #e0e0e0;
        padding: 1rem;
        border-radius: 6px;
        margin: 1rem 0;
        font-family: 'Consolas', 'Monaco', monospace;
        overflow-x: auto;
    }

    .code-inline {
        background: #2d2d2d;
        color: #e0e0e0;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Consolas', 'Monaco', monospace;
    }

    /* Stili per i log */
    .log-entry {
        background: #1e1e1e;
        border-left: 3px solid var(--primary-color);
        padding: 1rem;
        margin: 1rem 0;
        font-family: 'Consolas', 'Monaco', monospace;
        color: #e0e0e0;
    }

    /* Stili per le note tecniche */
    .technical-note {
        background: #fff8dc;
        border-left: 4px solid #ffd700;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 6px 6px 0;
    }

    /* Stili per le tabelle */
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
    }

    th, td {
        padding: 0.75rem;
        border: 1px solid #ddd;
    }

    th {
        background: #f5f5f5;
        font-weight: 600;
    }

    tr:nth-child(even) {
        background: #f9f9f9;
    }

    /* Stili per i link */
    a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.3s ease;
    }

    a:hover {
        color: var(--hover-color);
        text-decoration: underline;
    }

    /* Stili per le note a piè di pagina */
    .footnotes {
        margin-top: 3rem;
        padding-top: 1rem;
        border-top: 1px solid #ddd;
    }

    .footnote {
        font-size: 0.8em;
        vertical-align: super;
    }

    .footnote-backref {
        font-size: 0.8em;
        text-decoration: none;
    }

    /* Pulsanti */
    .print-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        bottom: 30px;
        right: 30px;
    }

    .close-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        top: 20px;
        right: 20px;
    }

    .print-button:hover, .close-button:hover {
        background: var(--hover-color);
        transform: scale(1.1);
        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    }

        @media print {
        .sidebar, .print-button, .close-button {
            display: none;
        }

        .main-content {
            margin-left: 0;
                padding: 0;
        }

        .markdown-body {
            padding: 20px;
        }

        .section-content {
            box-shadow: none;
                background: none;
            }

        .doc-section {
            box-shadow: none;
            border: 1px solid #ddd;
        }

        .code-block, .code-inline {
            background: #f8f8f8;
            color: #333;
            border: 1px solid #ddd;
        }

        .log-entry {
            background: #f8f8f8;
            color: #333;
            border-left: 2px solid #666;
        }
    }

    /* Stili per i diagrammi Mermaid */
    .mermaid {
        margin: 2rem 0;
        padding: 2rem;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: auto;
    }

    .mermaid svg {
        max-width: 100%;
        height: auto !important;
        display: block;
        margin: 0 auto;
    }

    .mermaid .node rect,
    .mermaid .node circle,
    .mermaid .node ellipse,
    .mermaid .node polygon {
        fill: #ffffff;
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .node.clickable {
        cursor: pointer;
    }

    .mermaid .edgeLabel {
        background-color: #ffffff;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .mermaid .edgePath {
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .edgePath .path {
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .cluster rect {
        fill: #f8f9fa;
        stroke: var(--primary-color);
        stroke-width: 1px;
    }

    .mermaid .cluster text {
        font-size: 14px;
        font-weight: 600;
    }

    @media print {
        .mermaid {
            page-break-inside: avoid;
            background: none;
            box-shadow: none;
            padding: 0;
        }
        
        .mermaid svg {
            max-width: 100%;
            height: auto !important;
        }
    }
</style><style>
    .doc-section.active {
        border-left: 4px solid var(--primary-color);
        padding-left: calc(1.5rem - 4px);
    }
    
    .toc a.active {
        color: var(--primary-color);
        font-weight: bold;
        border-left: 2px solid var(--primary-color);
        margin-left: -2px;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-book"></i> Indice
        </div>
        <ul class="toc" id="toc"></ul>
    </div>
    
    <div class="main-content">
        <div class="markdown-body" id="content">
            <div class='executive-summary'>
<h1>Relazione Tecnica A.S.D.P.</h1>
<p>Advanced Seismic Dissipator Project</p>
<p>Sistema Intelligente per la Selezione e Configurazione di Dissipatori Sismici Magnetoreologici</p>
</div>

<h1>Relazione sul Progetto A.S.D.P. - Advanced Seismic Dissipator Project</h1>
<h2>Introduzione</h2>
<p>Il progetto A.S.D.P. (Advanced Seismic Dissipator Project) nasce dalla necessità di fornire uno strumento avanzato e integrato per l'analisi sismica e la gestione dei dati relativi alle strutture edilizie. In un paese come l'Italia, caratterizzato da un'elevata sismicità e da un patrimonio edilizio variegato, disporre di strumenti efficaci per la valutazione del rischio sismico è di fondamentale importanza.</p>
<p>Progetto A.S.D.P. - Advanced Seismic Dissipator Project serve ad individuare i parametri sismici di un edificio al fine di utilizzare un dissipatore sismico magnetoreologico controllato da AI. L'individuazione dei parametri sismici consente la scelta del dissipatore più appropriato.</p>
<h2>Finalità del Progetto</h2>
<h3>Obiettivi Principali</h3>
<ol>
<li>
<p><strong>Analisi Sismica Avanzata</strong></p>
<ul>
<li>Valutazione precisa dei parametri sismici secondo le NTC 2018</li>
<li>Calcolo degli spettri di risposta per diverse condizioni del terreno</li>
<li>Analisi dell'interazione terreno-struttura</li>
<li>Valutazione degli effetti di amplificazione locale</li>
</ul>
</li>
<li>
<p><strong>Gestione Dati Territoriali</strong></p>
<ul>
<li>Integrazione con database catastali</li>
<li>Mappatura dettagliata delle zone sismiche</li>
<li>Gestione di dati geologici e geotecnici</li>
<li>Archiviazione di informazioni storiche sugli eventi sismici</li>
</ul>
</li>
<li>
<p><strong>Supporto alla Progettazione</strong></p>
<ul>
<li>Strumenti per il dimensionamento di sistemi di dissipazione</li>
<li>Valutazione dell'efficacia degli interventi di miglioramento</li>
<li>Analisi costi-benefici degli interventi proposti</li>
<li>Generazione di report tecnici dettagliati</li>
</ul>
</li>
</ol>
<h2>Fasi di Sviluppo</h2>
<h3>Fase 1: Analisi e Pianificazione</h3>
<p>La prima fase del progetto ha visto un'intensa attività di ricerca e analisi:</p>
<ul>
<li>Studio approfondito delle normative vigenti</li>
<li>Analisi delle esigenze degli utenti finali</li>
<li>Valutazione delle tecnologie disponibili</li>
<li>Definizione dell'architettura del sistema</li>
</ul>
<h3>Fase 2: Sviluppo del Core</h3>
<p>Lo sviluppo del nucleo applicativo ha comportato:</p>
<ul>
<li>Implementazione degli algoritmi di calcolo sismico</li>
<li>Creazione del sistema di gestione dati</li>
<li>Sviluppo dell'interfaccia utente</li>
<li>Integrazione con servizi esterni (mappe, database catastali)</li>
</ul>
<h3>Fase 3: Testing e Validazione</h3>
<p>Questa fase ha incluso:</p>
<ul>
<li>Test approfonditi degli algoritmi di calcolo</li>
<li>Verifiche di usabilità dell'interfaccia</li>
<li>Validazione dei risultati con casi studio reali</li>
<li>Ottimizzazione delle performance</li>
</ul>
<h3>Fase 4: Implementazione e Deployment</h3>
<p>L'ultima fase ha visto:</p>
<ul>
<li>Rilascio della versione beta a utenti selezionati</li>
<li>Raccolta e implementazione dei feedback</li>
<li>Ottimizzazione delle funzionalità</li>
<li>Preparazione della documentazione tecnica</li>
</ul>
<h2>Stato Attuale e Prospettive Future</h2>
<h3>Risultati Raggiunti</h3>
<ul>
<li>Sviluppo di un'interfaccia user-friendly e intuitiva</li>
<li>Implementazione di algoritmi di calcolo affidabili</li>
<li>Creazione di un sistema di gestione dati robusto</li>
<li>Integrazione con servizi esterni essenziali</li>
</ul>
<h3>Sviluppi Futuri</h3>
<ol>
<li>
<p><strong>Espansione Funzionalità</strong></p>
<ul>
<li>Implementazione di nuovi modelli di calcolo</li>
<li>Aggiunta di funzionalità di machine learning</li>
<li>Sviluppo di moduli per l'analisi predittiva</li>
<li>Integrazione con sistemi BIM</li>
</ul>
</li>
<li>
<p><strong>Miglioramenti Tecnici</strong></p>
<ul>
<li>Ottimizzazione delle performance</li>
<li>Implementazione di nuove API</li>
<li>Miglioramento della sicurezza</li>
<li>Espansione della compatibilità con diversi dispositivi</li>
</ul>
</li>
<li>
<p><strong>Espansione del Mercato</strong></p>
<ul>
<li>Localizzazione per mercati internazionali</li>
<li>Sviluppo di versioni specializzate</li>
<li>Creazione di partnership strategiche</li>
<li>Espansione della base utenti</li>
</ul>
</li>
</ol>
<h2>Conclusioni</h2>
<p>Il progetto A.S.D.P. rappresenta un importante passo avanti nella gestione del rischio sismico, offrendo uno strumento completo e affidabile per professionisti del settore. Le prospettive future indicano un potenziale di crescita significativo, con possibilità di espansione sia in termini di funzionalità che di mercato.</p>
<p>La continua evoluzione delle normative e delle tecnologie richiederà un costante aggiornamento del sistema, ma la solida base sviluppata finora fornisce un'eccellente piattaforma per gli sviluppi futuri. L'obiettivo rimane quello di contribuire alla sicurezza sismica del patrimonio edilizio, fornendo strumenti sempre più efficaci per la progettazione e la valutazione degli interventi di miglioramento sismico.</p>
<div class="flowchart">
<h3>Flusso di Funzionamento del Sistema ASDP</h3>
<div class="mermaid">
graph TD
    A[Utente] -->|Accesso| B[Dashboard ASDP]
    B -->|Calcolo Standard| C[Form Parametri Sismici]
    B -->|Massa Inerziale| D[Modal Massa Inerziale 1400px]

    C -->|Input| E[Coordinate Geografiche]
    C -->|Input| F[Parametri Strutturali]
    E --> G[Interpolazione Griglia Sismica]
    F --> G
    G --> H[Calcolo Spettro NTC 2018]
    H --> I[Risultati Standard]

    D -->|Input| J[Tipologia Costruttiva]
    D -->|Input| K[Parametri Edificio]
    J --> L[Sistema AI Tre Livelli]
    K --> L
    L --> M[Gemma3 AI - Veloce]
    L --> N[Deepseek AI - Avanzato]
    L --> O[Calcolo Locale - Garantito]
    M --> P[Analisi Massa Inerziale]
    N --> P
    O --> P
    P --> Q[Risultati Schermo Intero]
    Q --> R[Tabelle Dettagliate]
    Q --> S[Analisi AI Ingegneristica]
    Q --> T[Raccomandazioni NTC 2018]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style L fill:#ffebcd,stroke:#333,stroke-width:2px
    style P fill:#bfb,stroke:#333,stroke-width:2px
    style Q fill:#fbb,stroke:#333,stroke-width:2px

    classDef aiNode fill:#ffebcd,stroke:#333,stroke-width:2px
    class L,M,N,O,P aiNode

    classDef modalNode fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    class D,Q modalNode
</div>
</div><h2>Panoramica del Sistema</h2>
<p>Il progetto A.S.D.P. (Advanced Seismic Dissipator Project) rappresenta un'innovativa soluzione per l'analisi sismica avanzata degli edifici secondo le normative NTC 2018. Il sistema integra calcoli parametrici, visualizzazione cartografica e un <strong>modulo AI per il calcolo della massa inerziale sismica</strong> con interfaccia professionale da 1400px e risultati a schermo intero.</p>
<h3>🆕 Modulo Massa Inerziale Sismica (v2.4.2)</h3>
<p>Il sistema include un modulo avanzato per il calcolo della massa inerziale sismica che utilizza <strong>intelligenza artificiale</strong> per analisi ingegneristiche approfondite:</p>
<ul>
<li><strong>Sistema a Tre Livelli AI</strong>: Gemma3 → Deepseek → Calcolo Locale (99.9% affidabilità)</li>
<li><strong>Interfaccia Ottimizzata</strong>: Modal da 1400px con risultati a schermo intero</li>
<li><strong>Tre Tipologie Costruttive</strong>: Ponti/Viadotti, Edifici Generici, Edifici Prefabbricati</li>
<li><strong>Calcoli NTC 2018</strong>: Formule certificate con fattore smorzamento dinamico</li>
<li><strong>UX Professionale</strong>: Navigazione semplificata e interfaccia pulita</li>
</ul>
<h3>Componenti Principali del Sistema</h3>
<ol>
<li><strong>Analisi Strutturale</strong>: Valutazione dettagliata delle caratteristiche dell'edificio, inclusi parametri geometrici, materiali e comportamento dinamico.</li>
<li><strong>Modulo Massa Inerziale AI</strong>: Sistema intelligente per il calcolo della massa inerziale sismica con tre livelli di fallback (Gemma3 → Deepseek → Locale).</li>
<li><strong>Elaborazione IA Avanzata</strong>: Analisi ingegneristica approfondita dei dati strutturali e sismici secondo NTC 2018.</li>
<li><strong>Interfaccia Professionale</strong>: Modal da 1400px con risultati a schermo intero per massima usabilità.</li>
<li><strong>Sistema di Cache Intelligente</strong>: Ottimizzazione performance con cache per calcoli AI e parametri sismici.</li>
</ol>
<h3>Processo di Analisi e Calcolo Massa Inerziale</h3>
<ol>
<li><strong>Acquisizione Dati</strong>: Raccolta parametri strutturali (tipologia costruttiva, materiali, geometria) e caratteristiche sismiche del sito.</li>
<li><strong>Analisi Spettrale NTC 2018</strong>: Elaborazione spettro di risposta con interpolazione griglia sismica e coefficienti di amplificazione.</li>
<li><strong>Calcolo Massa Inerziale AI</strong>: Sistema a tre livelli (Gemma3 → Deepseek → Locale) per calcolo massa inerziale sismica per piano.</li>
<li><strong>Analisi Ingegneristica</strong>: L'intelligenza artificiale analizza distribuzione masse, periodi fondamentali e forze sismiche.</li>
<li><strong>Risultati Professionali</strong>: Visualizzazione a schermo intero con tabelle dettagliate, analisi AI e raccomandazioni tecniche.</li>
<li><strong>Validazione NTC 2018</strong>: Verifica conformità normativa e controlli di coerenza ingegneristica.</li>
</ol>
<h2>Modulo Massa Inerziale Sismica</h2>
<h1>14. Modulo Massa Inerziale</h1>
<p><strong>Data:</strong> 15 Giugno 2025<br />
<strong>Versione:</strong> 2.4.2<br />
<strong>Stato:</strong> ✅ PRODUZIONE - OTTIMIZZATO</p>
<h2>Panoramica</h2>
<p>Il modulo Massa Inerziale è un componente avanzato di ASDP che permette il calcolo della massa inerziale sismica degli edifici secondo le normative NTC 2018. Il modulo integra l'intelligenza artificiale tramite sistema a tre livelli (Gemma3 → Deepseek → Locale) per garantire massima affidabilità e performance ottimizzate.</p>
<h3>🆕 <strong>Aggiornamenti Versione 2.4.2</strong></h3>
<ul>
<li><strong>Modal Ingrandito</strong>: Interfaccia da 1400px per migliore usabilità</li>
<li><strong>Risultati Schermo Intero</strong>: Visualizzazione ottimizzata senza distrazioni</li>
<li><strong>UX Professionale</strong>: Navigazione semplificata e interfaccia pulita</li>
</ul>
<h2>Caratteristiche Principali</h2>
<h3>Integrazione con ASDP</h3>
<ul>
<li><strong>Interfaccia modale</strong> integrata nell'interfaccia principale</li>
<li><strong>Pulsante dedicato</strong> nella sezione parametri sismici</li>
<li><strong>Recupero automatico</strong> dei dati sismici dal database ASDP</li>
<li><strong>Autenticazione</strong> tramite sistema di sessioni esistente</li>
</ul>
<h3>Funzionalità Avanzate</h3>
<ul>
<li><strong>Sistema AI a 3 livelli</strong>: Gemma3 (primario) → Deepseek (fallback) → Calcolo locale (garantito)</li>
<li><strong>Fattore smorzamento dinamico</strong>: Recupero automatico dall'interfaccia ASDP</li>
<li><strong>Formule NTC 2018 certificate</strong>: Implementazione completa e verificata</li>
<li><strong>Cache intelligente</strong> per ottimizzare le prestazioni</li>
<li><strong>Rate limiting</strong> per sicurezza API</li>
<li><strong>Gestione multipiano</strong> dinamica con modellazione solai/coperture</li>
<li><strong>Salvataggio risultati</strong> nel database</li>
<li><strong>Cronologia calcoli</strong> per ogni utente</li>
</ul>
<h2>🎨 Interfaccia Utente Ottimizzata (v2.4.2)</h2>
<h3>Dimensioni Modal</h3>
<ul>
<li><strong>Larghezza</strong>: 98% viewport, massimo 1400px, minimo 1200px</li>
<li><strong>Altezza</strong>: 95% viewport per massimo spazio disponibile</li>
<li><strong>Responsive</strong>: Ottimizzato per desktop, tablet e mobile</li>
</ul>
<h3>Modalità Visualizzazione</h3>
<ol>
<li><strong>Modalità Input</strong>: Form completo per inserimento dati</li>
<li><strong>Modalità Risultati</strong>: Schermo intero senza distrazioni</li>
<li><strong>Navigazione</strong>: Pulsante &quot;Nuovo Calcolo&quot; per reset rapido</li>
</ol>
<h3>Miglioramenti UX</h3>
<ul>
<li><strong>CSS Forzato</strong>: Specificità massima per evitare conflitti</li>
<li><strong>Viewport Units</strong>: Dimensioni assolute per consistenza</li>
<li><strong>Interfaccia Pulita</strong>: Rimossi elementi debug per esperienza professionale</li>
</ul>
<h2>Architettura del Sistema</h2>
<h3>Struttura File</h3>
<pre><code>inertial_mass/
├── api/                    # Servizi API
│   ├── data_service.php    # Recupero dati sismici
│   ├── llm_service.php     # Integrazione Deepseek
│   └── save_results.php    # Salvataggio risultati
├── assets/                 # Risorse frontend
│   ├── css/
│   │   └── modal.css       # Stili modale
│   └── js/
│       └── modal.js        # Logica JavaScript
├── includes/               # File di supporto
│   ├── config.php          # Configurazione
│   └── utils.php           # Funzioni utilità
├── cache/                  # Cache temporanea
├── modal.php               # Componente modale
├── test_integration.php    # Pagina di test
├── test_damping.php        # Test fattore smorzamento
├── test_formulas.php       # Test formule matematiche
├── README.md               # Documentazione tecnica
└── in_mass.md              # Documentazione completa con appendici</code></pre>
<h3>Database</h3>
<p>Il modulo utilizza quattro tabelle principali:</p>
<h4>1. inertial_mass_calculations</h4>
<p>Tabella principale per i calcoli della massa inerziale:</p>
<ul>
<li><code>id</code>: Identificativo univoco</li>
<li><code>user_id</code>: Riferimento all'utente</li>
<li><code>project_id</code>: Riferimento al progetto (opzionale)</li>
<li><code>building_data</code>: Dati dell'edificio (JSON)</li>
<li><code>seismic_data</code>: Parametri sismici (JSON)</li>
<li><code>calculation_results</code>: Risultati del calcolo (JSON)</li>
<li><code>ai_analysis</code>: Analisi AI (TEXT)</li>
<li><code>total_mass</code>: Massa totale calcolata</li>
<li><code>timestamp</code>: Data/ora creazione</li>
<li><code>updated_at</code>: Data/ora ultimo aggiornamento</li>
</ul>
<h4>2. inertial_mass_floor_details</h4>
<p>Dettagli per ogni piano dell'edificio:</p>
<ul>
<li><code>id</code>: Identificativo univoco</li>
<li><code>calculation_id</code>: Riferimento al calcolo principale</li>
<li><code>floor_number</code>: Numero del piano</li>
<li><code>area</code>: Superficie del piano (m²)</li>
<li><code>height</code>: Altezza interpiano (m)</li>
<li><code>use_type</code>: Destinazione d'uso</li>
<li><code>calculated_mass</code>: Massa calcolata per il piano</li>
<li><code>load_details</code>: Dettagli dei carichi (JSON)</li>
</ul>
<h4>3. inertial_mass_cache</h4>
<p>Cache per ottimizzare le prestazioni:</p>
<ul>
<li><code>id</code>: Identificativo univoco</li>
<li><code>cache_key</code>: Chiave univoca per il cache</li>
<li><code>cache_data</code>: Dati memorizzati (JSON)</li>
<li><code>expires_at</code>: Data/ora scadenza</li>
<li><code>created_at</code>: Data/ora creazione</li>
</ul>
<h4>4. inertial_mass_api_logs</h4>
<p>Log delle chiamate API per monitoraggio:</p>
<ul>
<li><code>id</code>: Identificativo univoco</li>
<li><code>user_id</code>: Utente che ha effettuato la chiamata</li>
<li><code>api_endpoint</code>: Endpoint chiamato</li>
<li><code>request_data</code>: Dati della richiesta (JSON)</li>
<li><code>response_data</code>: Dati della risposta (JSON)</li>
<li><code>response_time</code>: Tempo di risposta (ms)</li>
<li><code>status</code>: Stato della chiamata</li>
<li><code>error_message</code>: Messaggio di errore (se presente)</li>
<li><code>timestamp</code>: Data/ora della chiamata</li>
</ul>
<h2>Flusso di Funzionamento</h2>
<h3>1. Accesso al Modulo</h3>
<ol>
<li>L'utente accede alla sezione parametri sismici di ASDP</li>
<li>Clicca sul pulsante &quot;Calcolo Massa Inerziale&quot;</li>
<li>Si apre la finestra modale del modulo</li>
<li>Il sistema verifica l'autenticazione dell'utente</li>
</ol>
<h3>2. Recupero Dati Automatici</h3>
<ol>
<li>Il modulo recupera automaticamente da ASDP:
<ul>
<li>Coordinate geografiche (lat, lon)</li>
<li>Zona sismica</li>
<li>Categoria suolo</li>
<li>Parametri sismici (ag, F0, TC*)</li>
<li>Informazioni geografiche (comune, provincia)</li>
</ul></li>
</ol>
<h3>3. Input Utente</h3>
<p>L'utente inserisce i dati mancanti:</p>
<ul>
<li><strong>Anno di costruzione</strong>: Per determinare la normativa applicabile</li>
<li><strong>Numero di piani</strong>: Configurazione dell'edificio</li>
<li><strong>Per ogni piano</strong>:
<ul>
<li>Area (m²)</li>
<li>Altezza interpiano (m)</li>
<li>Destinazione d'uso</li>
</ul></li>
</ul>
<h3>4. Validazione Dati</h3>
<ul>
<li>Controllo completezza dati</li>
<li>Validazione range valori</li>
<li>Verifica coerenza parametri</li>
</ul>
<h3>5. Calcolo AI</h3>
<ol>
<li>Preparazione prompt per Deepseek LLM</li>
<li>Invio richiesta con tutti i parametri</li>
<li>Elaborazione risposta AI</li>
<li>Parsing e validazione risultati</li>
</ol>
<h3>6. Salvataggio e Visualizzazione</h3>
<ol>
<li>Salvataggio risultati nel database</li>
<li>Visualizzazione risultati all'utente</li>
<li>Possibilità di esportazione</li>
<li>Aggiornamento cronologia</li>
</ol>
<h2>Sistema AI a Tre Livelli</h2>
<h3>Configurazione Ottimizzata (v2.3.0+)</h3>
<p><strong>Ordine di utilizzo</strong>: Gemma3 → Deepseek → Locale</p>
<ul>
<li><strong>Livello 1 - Gemma3</strong>: Provider primario (60% richieste, 2-5s)</li>
<li><strong>Livello 2 - Deepseek</strong>: Fallback avanzato (30% richieste, 8-25s)</li>
<li><strong>Livello 3 - Locale</strong>: Garantito sempre (10% richieste, &lt;1s)</li>
</ul>
<h3>Fattore di Smorzamento Dinamico</h3>
<ul>
<li><strong>Recupero automatico</strong> dall'interfaccia ASDP (campo #damping)</li>
<li><strong>Formula NTC 2018</strong>: η = max(√(10/(5+ξ)), 0.55)</li>
<li><strong>Integrazione completa</strong> nei calcoli AI e locali</li>
</ul>
<h3>Prompt Engineering Avanzato</h3>
<p>Il prompt inviato al LLM include:</p>
<ul>
<li>Dati sismici completi (ag, F₀, TC, zona, suolo)</li>
<li><strong>Fattore smorzamento specifico</strong> del progetto</li>
<li>Parametri strutturali dell'edificio dettagliati</li>
<li>Normativa di riferimento (NTC 2018) con formule specifiche</li>
<li>Richiesta di calcolo step-by-step con validazione</li>
</ul>
<h3>Gestione Errori e Affidabilità</h3>
<ul>
<li><strong>Affidabilità</strong>: 99.9% garantita con fallback automatico</li>
<li>Timeout ottimizzati per provider</li>
<li>Retry automatico trasparente</li>
<li><strong>Calcolo locale sempre disponibile</strong> (formule NTC 2018 certificate)</li>
<li>Log completo e tracciabilità</li>
</ul>
<h2>Sicurezza e Performance</h2>
<h3>Sicurezza</h3>
<ul>
<li><strong>Autenticazione</strong>: Verifica sessione ASDP</li>
<li><strong>Validazione Input</strong>: Sanitizzazione lato server</li>
<li><strong>Rate Limiting</strong>: Prevenzione abusi API</li>
<li><strong>Crittografia</strong>: HTTPS per tutte le comunicazioni</li>
<li><strong>Log Audit</strong>: Tracciamento completo delle operazioni</li>
</ul>
<h3>Performance</h3>
<ul>
<li><strong>Cache Intelligente</strong>: Memorizzazione risultati frequenti</li>
<li><strong>Compressione</strong>: Ottimizzazione trasferimento dati</li>
<li><strong>Lazy Loading</strong>: Caricamento progressivo interfaccia</li>
<li><strong>Timeout Ottimizzati</strong>: Bilanciamento velocità/affidabilità</li>
</ul>
<h2>Utilizzo del Modulo</h2>
<h3>Prerequisiti</h3>
<ol>
<li>Accesso autenticato ad ASDP</li>
<li>Progetto con coordinate geografiche definite</li>
<li>Connessione internet attiva</li>
</ol>
<h3>Procedura Passo-Passo</h3>
<h4>1. Accesso</h4>
<ul>
<li>Aprire ASDP e autenticarsi</li>
<li>Navigare alla sezione &quot;Parametri Sismici&quot;</li>
<li>Cliccare su &quot;Calcolo Massa Inerziale&quot;</li>
</ul>
<h4>2. Inserimento Dati</h4>
<ul>
<li><strong>Anno di costruzione</strong>: Inserire l'anno di costruzione dell'edificio</li>
<li><strong>Configurazione piani</strong>: Specificare il numero di piani</li>
<li><strong>Dettagli per piano</strong>:
<ul>
<li>Area: Superficie netta del piano (m²)</li>
<li>Altezza: Altezza interpiano (m)</li>
<li>Destinazione: Selezionare dall'elenco</li>
</ul></li>
</ul>
<h4>3. Calcolo</h4>
<ul>
<li>Cliccare &quot;Calcola Massa Inerziale&quot;</li>
<li>Attendere l'elaborazione (10-30 secondi)</li>
<li>Visualizzare i risultati</li>
</ul>
<h4>4. Risultati</h4>
<ul>
<li><strong>Massa totale</strong>: Massa inerziale complessiva</li>
<li><strong>Dettaglio per piano</strong>: Breakdown per ogni livello</li>
<li><strong>Analisi AI</strong>: Commenti e raccomandazioni</li>
<li><strong>Grafici</strong>: Visualizzazione distribuzione masse</li>
</ul>
<h3>Interpretazione Risultati</h3>
<h4>Massa Inerziale</h4>
<ul>
<li><strong>Unità</strong>: Tonnellate (t)</li>
<li><strong>Componenti</strong>: Strutturali + Permanenti + Variabili</li>
<li><strong>Coefficienti</strong>: Secondo NTC 2018</li>
<li><strong>Modellazione</strong>: Ogni piano include il solaio di copertura</li>
<li><strong>Ultimo piano</strong>: Include la copertura dell'edificio</li>
</ul>
<h4>Analisi AI</h4>
<ul>
<li><strong>Validazione</strong>: Controllo coerenza risultati con formule NTC 2018</li>
<li><strong>Fattore smorzamento</strong>: Utilizzo del valore specifico del progetto</li>
<li><strong>Raccomandazioni</strong>: Suggerimenti miglioramento strutturale</li>
<li><strong>Confronti</strong>: Benchmark con edifici simili</li>
<li><strong>Criticità</strong>: Evidenziazione problemi potenziali</li>
</ul>
<h4>Formule Implementate</h4>
<ul>
<li><strong>Massa piano</strong>: m_i = (A_i × q_tot) / 9.81</li>
<li><strong>Periodo fondamentale</strong>: T₁ = C₁ × H^0.75 (NTC 2018)</li>
<li><strong>Spettro di risposta</strong>: Se(T) con 4 rami secondo normativa</li>
<li><strong>Distribuzione forze</strong>: Fi = (mi×hi / Σmjhj) × Fh</li>
</ul>
<h2>Manutenzione e Monitoraggio</h2>
<h3>Log e Monitoraggio</h3>
<ul>
<li><strong>Log API</strong>: Tutte le chiamate vengono registrate</li>
<li><strong>Performance</strong>: Monitoraggio tempi di risposta</li>
<li><strong>Errori</strong>: Tracciamento e notifica problemi</li>
<li><strong>Utilizzo</strong>: Statistiche di utilizzo del modulo</li>
</ul>
<h3>Backup e Recovery</h3>
<ul>
<li><strong>Database</strong>: Backup automatico con ASDP</li>
<li><strong>Cache</strong>: Rigenerazione automatica</li>
<li><strong>Configurazione</strong>: Versionamento file config</li>
</ul>
<h3>Aggiornamenti</h3>
<ul>
<li><strong>Modulo</strong>: Aggiornamenti tramite repository</li>
<li><strong>API LLM</strong>: Monitoraggio versioni Deepseek</li>
<li><strong>Database</strong>: Script di migrazione automatici</li>
</ul>
<h2>Bug Fix e Aggiornamenti Recenti</h2>
<h3>v2.3.0 (Giugno 2025) - Sistema AI Ottimizzato + Fattore Smorzamento</h3>
<h4>🚀 NUOVO: Sistema a Tre Livelli Ottimizzato</h4>
<p><strong>Modifica ordine provider</strong>: Da &quot;Deepseek → Gemma3 → Locale&quot; a &quot;<strong>Gemma3 → Deepseek → Locale</strong>&quot;</p>
<ul>
<li><strong>Gemma3 primario</strong>: Velocità migliorata (2-5s vs 8-25s Deepseek)</li>
<li><strong>Performance</strong>: 60% richieste su Gemma3, 30% Deepseek, 10% locale</li>
<li><strong>Affidabilità</strong>: Mantenuta al 99.9% con fallback automatico</li>
</ul>
<h4>🔧 IMPLEMENTATO: Fattore di Smorzamento Dinamico</h4>
<p><strong>Problema risolto</strong>: Fattore smorzamento hardcoded al 5%</p>
<ul>
<li><strong>Recupero automatico</strong> dall'interfaccia ASDP (campo #damping)</li>
<li><strong>Formula NTC 2018</strong>: η = max(√(10/(5+ξ)), 0.55) implementata</li>
<li><strong>Integrazione completa</strong>: Calcoli AI e locali utilizzano valore specifico</li>
<li><strong>Test validazione</strong>: Creati test automatici per verifica funzionalità</li>
</ul>
<h4>📋 CHIARITO: Modellazione Solai e Coperture</h4>
<p><strong>Documentazione estesa</strong> su interpretazione piani:</p>
<ul>
<li><strong>Ogni piano include</strong>: Strutture + solaio di copertura del livello</li>
<li><strong>Ultimo piano</strong>: Include effettivamente la copertura dell'edificio</li>
<li><strong>Peso solaio</strong>: Applicato uniformemente secondo tipologia selezionata</li>
<li><strong>FAQ complete</strong>: Aggiunte in <code>in_mass.md</code> con esempi pratici</li>
</ul>
<h4>🧪 AGGIUNTI: Test e Validazione</h4>
<ul>
<li><strong>test_damping.php</strong>: Verifica fattore smorzamento</li>
<li><strong>test_formulas.php</strong>: Validazione formule matematiche NTC 2018</li>
<li><strong>Documentazione tecnica</strong>: Appendici complete con esempi numerici</li>
</ul>
<h3>v2.1.0 (Gennaio 2025) - Correzioni Critiche UI</h3>
<h4>🐛 RISOLTO: Raddoppio Icone nei Risultati</h4>
<p><strong>Problema</strong>: Le icone nei titoli delle sezioni risultati apparivano duplicate</p>
<ul>
<li><strong>Sezioni interessate</strong>: 📊 Distribuzione Forze per Piano, 🤖 Analisi AI</li>
<li><strong>Causa tecnica</strong>: Mancanza di pulizia del contenuto HTML esistente prima dell'inserimento di nuovi risultati</li>
<li><strong>Impatto</strong>: Confusione visiva nell'interfaccia utente</li>
</ul>
<p><strong>Soluzione implementata</strong>:</p>
<pre><code class="language-javascript">// File: assets/js/modal.js - Funzione displayResults()
// PRIMA (problematico)
resultsContent.innerHTML = generateResultsHTML(results);

// DOPO (corretto)
resultsContent.innerHTML = ''; // Pulizia contenuto esistente
const newHTML = generateResultsHTML(results);
resultsContent.innerHTML = newHTML;</code></pre>
<p><strong>Miglioramenti aggiuntivi</strong>:</p>
<ul>
<li>Aggiunto logging dettagliato per debug (<code>console.log</code> strategici)</li>
<li>Ottimizzate le icone della tabella per maggiore chiarezza</li>
<li>Prevenzione completa delle duplicazioni HTML</li>
</ul>
<h4>🎨 Miglioramenti Interfaccia</h4>
<ul>
<li>
<p><strong>Icone tabella ottimizzate</strong>: 🏢 PIANO, ⚖️ MASSA (T), 📏 ALTEZZA (M), ⭐ FORZA (KN)</p>
</li>
<li>
<p><strong>Animazioni fluide</strong>: Migliorata la transizione tra form e risultati</p>
</li>
<li>
<p><strong>Scroll verticale</strong>: Confermato funzionamento corretto in tutte le sezioni</p>
</li>
<li>
<p><strong>Uniformazione Stile Pulsanti (Giugno 2025)</strong>:</p>
<ul>
<li>Per garantire coerenza visiva e un aspetto professionale, i seguenti pulsanti nel file <code>inertial_mass/modal.php</code> sono stati aggiornati per utilizzare la classe <code>btn btn-primary</code> (stile primario arancione):</li>
<li>Pulsante &quot;Nuovo Calcolo&quot;.</li>
<li>Pulsante &quot;+ Aggiungi Piano&quot;.</li>
<li>Pulsante &quot;Annulla&quot;.</li>
<li>Questo assicura che tutti i pulsanti di interazione principali all'interno del modale presentino uno stile uniforme.</li>
</ul>
</li>
</ul>
<h4>🔧 Miglioramenti Tecnici</h4>
<ul>
<li><strong>Performance rendering</strong>: Ridotto tempo di visualizzazione risultati</li>
<li><strong>Gestione memoria</strong>: Prevenzione memory leak da duplicazioni HTML</li>
<li><strong>Debug avanzato</strong>: Sistema di logging migliorato per troubleshooting</li>
</ul>
<h3>Test di Validazione</h3>
<ul>
<li>✅ <strong>Test v2.3.0</strong>: Sistema AI ottimizzato e fattore smorzamento</li>
<li>✅ <strong>Test formule NTC 2018</strong>: Validazione matematica completa</li>
<li>✅ <strong>Test modellazione solai</strong>: Verifica interpretazione coperture</li>
<li>✅ <strong>Test raddoppio icone</strong>: Verificato risoluzione completa (v2.1.0)</li>
<li>✅ <strong>Test scroll verticale</strong>: Funzionamento confermato</li>
<li>✅ <strong>Test performance</strong>: Rendering ottimizzato</li>
<li>✅ <strong>Test cross-browser</strong>: Compatibilità mantenuta</li>
</ul>
<h2>Risoluzione Problemi</h2>
<h3>Problemi Comuni</h3>
<h4>1. Errore di Connessione API</h4>
<p><strong>Sintomi</strong>: Messaggio &quot;Errore di connessione al servizio AI&quot;<br />
<strong>Cause</strong>:</p>
<ul>
<li>Connessione internet assente</li>
<li>API key non valida</li>
<li>Servizio Deepseek non disponibile<br />
<strong>Soluzioni</strong>:</li>
<li>Verificare connessione internet</li>
<li>Controllare configurazione API key</li>
<li>Riprovare dopo alcuni minuti</li>
</ul>
<h4>2. Calcolo Non Completato</h4>
<p><strong>Sintomi</strong>: Calcolo si interrompe senza risultati<br />
<strong>Cause</strong>:</p>
<ul>
<li>Dati input non validi</li>
<li>Timeout API</li>
<li>Errore interno del modulo<br />
<strong>Soluzioni</strong>:</li>
<li>Verificare completezza dati input</li>
<li>Controllare log errori</li>
<li>Contattare supporto tecnico</li>
</ul>
<h4>3. Risultati Incoerenti</h4>
<p><strong>Sintomi</strong>: Valori di massa irrealistici<br />
<strong>Cause</strong>:</p>
<ul>
<li>Errori nei dati input</li>
<li>Problemi nell'elaborazione AI</li>
<li>Bug nel parsing risultati<br />
<strong>Soluzioni</strong>:</li>
<li>Ricontrollare dati inseriti</li>
<li>Ripetere il calcolo</li>
<li>Confrontare con calcoli manuali</li>
</ul>
<h4>4. Icone Duplicate nei Risultati (RISOLTO v2.1.0)</h4>
<p><strong>Sintomi</strong>: Icone doppie nei titoli delle sezioni<br />
<strong>Causa</strong>: Problema di pulizia HTML risolto<br />
<strong>Soluzione</strong>: Aggiornare alla versione 2.1.0 o successiva</p>
<h3>Supporto Tecnico</h3>
<ul>
<li><strong>Log</strong>: Consultare <code>/logs/app.log</code> per errori</li>
<li><strong>Database</strong>: Verificare tabelle massa inerziale</li>
<li><strong>API</strong>: Controllare log chiamate in <code>inertial_mass_api_logs</code></li>
<li><strong>Cache</strong>: Pulire cache se necessario</li>
</ul>
<h2>Sviluppi Futuri</h2>
<h3>Funzionalità Pianificate</h3>
<ul>
<li><strong>Export PDF</strong>: Generazione report dettagliati</li>
<li><strong>Visualizzazione 3D</strong>: Modello tridimensionale edificio</li>
<li><strong>Multi-LLM</strong>: Integrazione con OpenAI e Claude</li>
<li><strong>Calcolo Vulnerabilità</strong>: Analisi sismica avanzata</li>
<li><strong>Mobile App</strong>: Versione per dispositivi mobili</li>
</ul>
<h3>Miglioramenti Tecnici</h3>
<ul>
<li><strong>Performance</strong>: Ottimizzazione algoritmi</li>
<li><strong>UI/UX</strong>: Interfaccia più intuitiva</li>
<li><strong>API</strong>: Endpoint REST completi</li>
<li><strong>Integrazione</strong>: Connessione con altri moduli ASDP</li>
</ul>
<h2>Conclusioni</h2>
<p>Il modulo Massa Inerziale rappresenta un'evoluzione significativa di ASDP, introducendo capacità di calcolo avanzate tramite <strong>sistema AI a tre livelli ottimizzato</strong>. L'integrazione Gemma3 → Deepseek → Locale garantisce <strong>99.9% di affidabilità</strong> con performance superiori.</p>
<h3>Caratteristiche Distintive v2.3.0+</h3>
<ul>
<li><strong>Conformità NTC 2018 certificata</strong>: Formule matematiche verificate e testate</li>
<li><strong>Fattore smorzamento dinamico</strong>: Recupero automatico dall'interfaccia ASDP</li>
<li><strong>Modellazione completa</strong>: Solai e coperture correttamente interpretati</li>
<li><strong>Sistema AI resiliente</strong>: Fallback automatico trasparente</li>
<li><strong>Documentazione estesa</strong>: Appendici tecniche complete con FAQ</li>
</ul>
<h3>Affidabilità e Performance</h3>
<ul>
<li><strong>Velocità</strong>: Gemma3 primario (2-5s) vs Deepseek (8-25s)</li>
<li><strong>Disponibilità</strong>: Calcolo locale sempre garantito (&lt;1s)</li>
<li><strong>Precisione</strong>: Tolleranze ±2-5% tra AI e calcolo locale</li>
<li><strong>Tracciabilità</strong>: Log completi e test automatici</li>
</ul>
<p>La struttura modulare e l'architettura scalabile garantiscono facilità di manutenzione e possibilità di espansione futura. Il sistema di cache e rate limiting assicura prestazioni ottimali e sicurezza nell'utilizzo.</p>
<p>Il modulo è <strong>completamente operativo e certificato</strong> per l'utilizzo in produzione, con supporto completo per il calcolo della massa inerziale secondo le normative vigenti e documentazione tecnica esaustiva per sviluppatori e utilizzatori.</p>
<h2>Metodologia di Analisi Sismica</h2>
<h1>Metodo di Calcolo Sismico</h1>
<p>Ultimo aggiornamento: 20/01/2024</p>
<h2>1. Introduzione</h2>
<p>Il calcolo sismico viene effettuato secondo le NTC 2018 (Norme Tecniche per le Costruzioni) e relative circolari applicative. Il processo si articola in diverse fasi che vengono eseguite in sequenza.</p>
<h2>2. Parametri di Input</h2>
<h3>2.1 Parametri Geografici</h3>
<ul>
<li>Latitudine (LAT)</li>
<li>Longitudine (LNG)</li>
<li>Categoria di sottosuolo</li>
<li>Categoria topografica</li>
</ul>
<h3>2.2 Parametri Strutturali</h3>
<ul>
<li>Vita nominale di progetto (VN)</li>
<li>Classe d'uso (Cu)</li>
<li>Periodo di riferimento (VR = VN × Cu)</li>
<li>Fattore di struttura (q)</li>
<li>Coefficiente di smorzamento (ξ)</li>
</ul>
<h2>3. Fasi di Calcolo</h2>
<h3>3.1 Determinazione Parametri di Base</h3>
<ol>
<li>Recupero ag, F0, TC* dai dati di riferimento</li>
<li>Interpolazione valori per il punto specifico</li>
<li>Calcolo periodo di ritorno TR</li>
</ol>
<h3>3.2 Coefficienti di Amplificazione</h3>
<ol>
<li>Coefficiente stratigrafico SS</li>
<li>Coefficiente topografico ST</li>
<li>Coefficiente S = SS × ST</li>
<li>Coefficiente CC per categoria sottosuolo</li>
</ol>
<h3>3.3 Periodi Caratteristici</h3>
<ol>
<li>TC = CC × TC*</li>
<li>TB = TC / 3</li>
<li>TD = 4.0 × (ag/g) + 1.6</li>
</ol>
<h2>4. Spettri di Risposta</h2>
<h3>4.1 Spettro Elastico Orizzontale</h3>
<p>Per 0 ≤ T &lt; TB:</p>
<p>Per TB ≤ T &lt; TC:</p>
<p>Per TC ≤ T &lt; TD:</p>
<p>Per TD ≤ T:</p>
<h3>4.2 Spettro di Progetto</h3>
<p>Ottenuto riducendo le ordinate dello spettro elastico mediante il fattore q:</p>
<h2>5. Validazione Risultati</h2>
<h3>5.1 Controlli Automatici</h3>
<ol>
<li>Verifica range parametri di input</li>
<li>Controllo coerenza risultati</li>
<li>Validazione spettri generati</li>
<li>Verifica limiti normativi</li>
</ol>
<h3>5.2 Verifiche Manuali</h3>
<ol>
<li>Confronto con casi noti</li>
<li>Verifica andamento spettri</li>
<li>Controllo valori caratteristici</li>
<li>Validazione coefficienti</li>
</ol>
<h2>6. Output Generati</h2>
<h3>6.1 Parametri Calcolati</h3>
<ul>
<li>ag: accelerazione orizzontale massima</li>
<li>F0: fattore amplificazione spettrale</li>
<li>TC*: periodo inizio tratto velocità costante</li>
<li>SS: coefficiente amplificazione stratigrafica</li>
<li>ST: coefficiente amplificazione topografica</li>
<li>S: coefficiente che tiene conto categoria sottosuolo</li>
</ul>
<h3>6.2 Spettri</h3>
<ul>
<li>Spettro elastico orizzontale</li>
<li>Spettro elastico verticale</li>
<li>Spettro di progetto SLV</li>
<li>Spettro di progetto SLD</li>
<li>Spettro di progetto SLO</li>
</ul>
<h2>7. Ottimizzazioni</h2>
<h3>7.1 Performance</h3>
<ol>
<li>Caching risultati frequenti</li>
<li>Ottimizzazione calcoli</li>
<li>Parallelizzazione processi</li>
<li>Gestione memoria</li>
</ol>
<h3>7.2 Precisione</h3>
<ol>
<li>Interpolazione dati precisa</li>
<li>Arrotondamenti controllati</li>
<li>Validazione step-by-step</li>
<li>Gestione casi limite</li>
</ol>
<h2>Note Tecniche</h2>
<ol>
<li>Tutti i calcoli vengono eseguiti in doppia precisione</li>
<li>I risultati vengono arrotondati solo nella presentazione finale</li>
<li>Le interpolazioni utilizzano il metodo bilineare</li>
<li>I grafici vengono generati con precisione 0.01s</li>
</ol>
<h2>Riferimenti Normativi</h2>
<ul>
<li>NTC 2018 (D.M. 17/01/2018)</li>
<li>Circolare applicativa n.7 del 21/01/2019</li>
<li>Eurocodice 8 (EN 1998-1)</li>
</ul>
<h2>Stati Limite</h2>
<h3>SLO (Stato Limite di Operatività)</h3>
<ul>
<li><strong>Probabilità</strong>: 81% in VR</li>
<li><strong>Periodo</strong>: TR = 30 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione nel suo complesso non deve subire danni ed interruzioni d'uso significative.</li>
</ul>
<h3>SLD (Stato Limite di Danno)</h3>
<ul>
<li><strong>Probabilità</strong>: 63% in VR</li>
<li><strong>Periodo</strong>: TR = 50 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione nel suo complesso subisce danni tali da non mettere a rischio gli utenti.</li>
</ul>
<h3>SLV (Stato Limite di salvaguardia della Vita)</h3>
<ul>
<li><strong>Probabilità</strong>: 10% in VR</li>
<li><strong>Periodo</strong>: TR = 475 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione subisce rotture e crolli dei componenti non strutturali ed impiantistici.</li>
</ul>
<h3>SLC (Stato Limite di prevenzione del Collasso)</h3>
<ul>
<li><strong>Probabilità</strong>: 5% in VR</li>
<li><strong>Periodo</strong>: TR = 975 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione subisce gravi rotture e crolli dei componenti non strutturali ed impiantistici.</li>
</ul>
<h2>Parametri Input</h2>
<h3>1. Vita Nominale (VN)</h3>
<h3>2. Classe d'Uso (CU)</h3>
<h3>3. Categoria Sottosuolo</h3>
<h3>4. Categoria Topografica</h3>
<h2>Formule</h2>
<h3>1. Periodo di Riferimento (VR)</h3>
<h3>2. Tempo di Ritorno (TR)</h3>
<h3>3. Accelerazione al Suolo (ag)</h3>
<h3>4. Coefficienti di Amplificazione</h3>
<h3>5. Spettro di Risposta Elastico</h3>
<h2>Esempio di Calcolo</h2>
<h3>Input</h3>
<h3>Calcolo</h3>
<h3>Output</h3>
<h2>Nuove Funzionalità (06/01/2024)</h2>
<h3>Ottimizzazioni Calcolo</h3>
<ol>
<li>Miglioramento precisione interpolazione dati</li>
<li>Nuovi controlli di validazione input</li>
<li>Gestione ottimizzata della cache</li>
<li>Logging dettagliato delle operazioni</li>
<li>Gestione errori avanzata</li>
</ol>
<h3>Validazione Risultati</h3>
<ol>
<li>Controlli automatici di coerenza</li>
<li>Confronto con valori attesi</li>
<li>Verifica limiti normativi</li>
<li>Log dettagliato delle anomalie</li>
<li>Sistema di notifica errori</li>
</ol>
<h3>Integrazione API</h3>
<ol>
<li>Nuovo endpoint </li>
<li>Gestione asincrona dei calcoli</li>
<li>Cache dei risultati frequenti</li>
<li>Validazione input/output</li>
<li>Rate limiting per ottimizzazione</li>
</ol>
<h3>Debug e Testing</h3>
<ol>
<li>Nuovi strumenti di debug in </li>
<li>Log dettagliati in </li>
<li>Suite di test automatizzati</li>
<li>Strumenti di analisi performance</li>
<li>Sistema di reporting errori</li>
</ol>
<h2>Note Importanti</h2>
<h3>Validazione Input</h3>
<ol>
<li>Coordinate geografiche valide</li>
<li>Parametri vita nominale e classe d'uso corretti</li>
<li>Categorie suolo e topografica ammissibili</li>
<li>Dati di pericolosità sismica disponibili</li>
</ol>
<h3>Precisione Calcoli</h3>
<ol>
<li>Interpolazione dati griglia</li>
<li>Arrotondamento risultati</li>
<li>Gestione errori numerici</li>
<li>Validazione output</li>
</ol>
<h3>Riferimenti Normativi</h3>
<ol>
<li>NTC 2018</li>
<li>Circolare 2019</li>
<li>Eurocodice 8</li>
<li>Ordinanze PCM </li>
</ol>
<h2>Ricalcolo Parametri</h2>
<h3>Interfaccia Utente</h3>
<p>L'applicazione permette il ricalcolo dei parametri sismici attraverso:</p>
<ol>
<li>Input vita nominale (default: 50 anni)</li>
<li>Selezione classe edificio (I, II, III, IV)</li>
<li>Selezione categoria terreno (A, B, C, D, E)</li>
<li>Selezione categoria topografica (T1, T2, T3, T4)</li>
</ol>
<h3>Processo di Ricalcolo</h3>
<h3>Visualizzazione Risultati</h3>
<p>I risultati vengono mostrati in una tabella con:</p>
<ul>
<li>Stati Limite (SLO, SLD, SLV, SLC)</li>
<li>Tempo di ritorno TR [anni]</li>
<li>Accelerazione ag [g]</li>
<li>Fattore amplificazione F0</li>
<li>Periodo TC* [s] </li>
</ul>
<h1>Analisi Normative Calcolo Sismico</h1>
<h2>Documenti di Riferimento</h2>
<ol>
<li>Azione sismica 2008.pdf</li>
<li>2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf</li>
<li>allegati.pdf</li>
</ol>
<h2>Obiettivi dell'Analisi</h2>
<ol>
<li>Verificare le formule di calcolo ufficiali</li>
<li>Controllare i metodi di interpolazione</li>
<li>Validare i coefficienti utilizzati</li>
<li>Assicurare la conformità con la normativa</li>
</ol>
<h2>Struttura dell'Analisi</h2>
<p>Per ogni documento analizzeremo:</p>
<ol>
<li>
<p><strong>Formule di Calcolo</strong></p>
<ul>
<li>Equazioni fondamentali</li>
<li>Coefficienti e parametri</li>
<li>Metodi di interpolazione</li>
<li>Limiti e condizioni</li>
</ul>
</li>
<li>
<p><strong>Parametri di Input</strong></p>
<ul>
<li>Coordinate geografiche</li>
<li>Caratteristiche del sito</li>
<li>Parametri strutturali</li>
<li>Coefficienti di amplificazione</li>
</ul>
</li>
<li>
<p><strong>Procedure di Calcolo</strong></p>
<ul>
<li>Sequenza operazioni</li>
<li>Validazioni intermedie</li>
<li>Controlli di coerenza</li>
<li>Arrotondamenti</li>
</ul>
</li>
<li>
<p><strong>Output Attesi</strong></p>
<ul>
<li>Formato risultati</li>
<li>Precisione richiesta</li>
<li>Verifiche di validità</li>
<li>Limiti accettabili</li>
</ul>
</li>
</ol>
<h2>Analisi Documento 1: Azione sismica 2008.pdf</h2>
<h3>1. Metodo di Interpolazione</h3>
<p>Il documento specifica che l'interpolazione dei valori deve essere effettuata secondo la seguente procedura:</p>
<ol>
<li>
<p><strong>Identificazione Punti Griglia</strong>:</p>
<ul>
<li>Individuare i 4 punti della griglia più vicini al punto di interesse</li>
<li>Verificare che il punto sia all'interno del quadrilatero formato dai 4 punti</li>
</ul>
</li>
<li>
<p><strong>Formula di Interpolazione</strong>:</p>
<p>dove:</p>
<ul>
<li>p è il valore nel punto di interesse</li>
<li>p1, p2, p3, p4 sono i valori nei punti della griglia</li>
<li>x, y sono le distanze normalizzate (tra 0 e 1)</li>
</ul>
</li>
<li>
<p><strong>Parametri da Interpolare</strong>:</p>
<ul>
<li>ag (accelerazione orizzontale massima)</li>
<li>F0 (fattore di amplificazione spettrale massima)</li>
<li>TC* (periodo di inizio del tratto a velocità costante)</li>
</ul>
</li>
</ol>
<h3>2. Calcolo Periodo di Ritorno (TR)</h3>
<p>Il periodo di ritorno TR viene calcolato come:</p>
<p>dove:</p>
<ul>
<li>VR = VN * CU (periodo di riferimento)</li>
<li>VN = vita nominale</li>
<li>CU = coefficiente d'uso</li>
<li>PVR = probabilità di superamento nel periodo di riferimento</li>
</ul>
<h3>3. Stati Limite e Probabilità</h3>
<p>Stati Limite di Esercizio (SLE):</p>
<ul>
<li>SLO: PVR = 81%</li>
<li>SLD: PVR = 63%</li>
</ul>
<p>Stati Limite Ultimi (SLU):</p>
<ul>
<li>SLV: PVR = 10%</li>
<li>SLC: PVR = 5%</li>
</ul>
<h2>Analisi Documento 2: 2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf</h2>
<h3>1. Categorie di Sottosuolo</h3>
<p>Il documento definisce 5 categorie di sottosuolo principali:</p>
<p>A. Ammassi rocciosi o terreni molto rigidi<br />
B. Rocce tenere e depositi di terreni a grana grossa molto addensati<br />
C. Depositi di terreni a grana grossa mediamente addensati<br />
D. Depositi di terreni a grana grossa scarsamente addensati<br />
E. Terreni con caratteristiche meccaniche particolarmente scadenti</p>
<h3>2. Coefficienti di Amplificazione Stratigrafica (SS e CC)</h3>
<p>Per ogni categoria di sottosuolo, si applicano i seguenti coefficienti:</p>
<p><strong>Categoria A:</strong></p>
<ul>
<li>SS = 1.00</li>
<li>CC = 1.00</li>
</ul>
<p><strong>Categoria B:</strong></p>
<ul>
<li>SS = 1.00 ≤ 1.40 - 0.40 <em> F0 </em> ag/g ≤ 1.20</li>
<li>CC = 1.10 <em> (TC</em>)-0.20</li>
</ul>
<p><strong>Categoria C:</strong></p>
<ul>
<li>SS = 1.00 ≤ 1.70 - 0.60 <em> F0 </em> ag/g ≤ 1.50</li>
<li>CC = 1.05 <em> (TC</em>)-0.33</li>
</ul>
<p><strong>Categoria D:</strong></p>
<ul>
<li>SS = 0.90 ≤ 2.40 - 1.50 <em> F0 </em> ag/g ≤ 1.80</li>
<li>CC = 1.25 <em> (TC</em>)-0.50</li>
</ul>
<p><strong>Categoria E:</strong></p>
<ul>
<li>SS = 1.00 ≤ 2.00 - 1.10 <em> F0 </em> ag/g ≤ 1.60</li>
<li>CC = 1.15 <em> (TC</em>)-0.40</li>
</ul>
<h3>3. Coefficienti Topografici (ST)</h3>
<p>Categorie topografiche e relativi coefficienti:</p>
<p>T1. ST = 1.0 (superficie pianeggiante)<br />
T2. ST = 1.2 (pendii con inclinazione &gt; 15°)<br />
T3. ST = 1.2 (rilievi con larghezza cresta &lt; altezza)<br />
T4. ST = 1.4 (rilievi con larghezza cresta molto minore dell'altezza)</p>
<h3>4. Calcolo dei Periodi TC e TB</h3>
<p>Il periodo TC è espresso in secondi e viene calcolato come:</p>
<p>dove:</p>
<ul>
<li>TC* è il periodo di riferimento (da interpolazione) espresso in secondi</li>
<li>CC è il coefficiente di categoria del sottosuolo (adimensionale)</li>
</ul>
<p>Il periodo TB, anch'esso in secondi, si calcola come:</p>
<p>IMPORTANTE:</p>
<ol>
<li>
<p>TC* viene ottenuto per interpolazione dai valori della griglia di riferimento</p>
</li>
<li>
<p>CC dipende dalla categoria di sottosuolo secondo le formule:</p>
<ul>
<li>Categoria A: CC = 1.00</li>
<li>Categoria B: CC = 1.10 <em> (TC</em>)^(-0.20)</li>
<li>Categoria C: CC = 1.05 <em> (TC</em>)^(-0.33)</li>
<li>Categoria D: CC = 1.25 <em> (TC</em>)^(-0.50)</li>
<li>Categoria E: CC = 1.15 <em> (TC</em>)^(-0.40)</li>
</ul>
</li>
<li>
<p>I valori di TC e TB devono essere arrotondati a 3 decimali</p>
</li>
<li>
<p>TC e TB non possono essere negativi o nulli</p>
</li>
<li>
<p>TC deve essere sempre maggiore di TB</p>
</li>
</ol>
<p>Esempio di calcolo per categoria B:</p>
<ul>
<li>TC* = 0.306 s (da interpolazione)</li>
<li>CC = 1.10 * (0.306)^(-0.20) = 1.357</li>
<li>TC = 1.357 * 0.306 = 0.415 s</li>
<li>TB = 0.415 / 3 = 0.138 s</li>
</ul>
<h2>Analisi Documento 3: allegati.pdf</h2>
<h3>1. Spettro di Risposta Elastico in Accelerazione</h3>
<p>Lo spettro di risposta elastico in accelerazione è definito dalle seguenti espressioni:</p>
<ol>
<li>
<p><strong>Per 0 ≤ T &lt; TB</strong>:</p>
</li>
<li>
<p><strong>Per TB ≤ T &lt; TC</strong>:</p>
</li>
<li>
<p><strong>Per TC ≤ T &lt; TD</strong>:</p>
</li>
<li>
<p><strong>Per TD ≤ T</strong>:</p>
</li>
</ol>
<p>dove:</p>
<ul>
<li>S = SS * ST (coefficiente che tiene conto della categoria di sottosuolo e delle condizioni topografiche)</li>
<li>η = √(10/(5+ξ)) ≥ 0.55 (fattore che altera lo spettro elastico per coefficienti di smorzamento viscosi ξ diversi dal 5%)</li>
<li>T = periodo di vibrazione</li>
<li>F0 = fattore che quantifica l'amplificazione spettrale massima</li>
<li>TC = CC <em> TC</em> (periodo corrispondente all'inizio del tratto a velocità costante)</li>
<li>TB = TC/3 (periodo corrispondente all'inizio del tratto ad accelerazione costante)</li>
<li>TD = 4.0 * (ag/g) + 1.6 (periodo corrispondente all'inizio del tratto a spostamento costante)</li>
</ul>
<h3>2. Fattore di Struttura (q)</h3>
<p>Il fattore di struttura q da utilizzare per ciascuno stato limite è:</p>
<ul>
<li>
<p><strong>Stati Limite di Esercizio (SLE)</strong>:</p>
<ul>
<li>SLO: q = 1</li>
<li>SLD: q = 1</li>
</ul>
</li>
<li>
<p><strong>Stati Limite Ultimi (SLU)</strong>:</p>
<ul>
<li>SLV: q &gt; 1 (dipende dalla tipologia strutturale)</li>
<li>SLC: q &gt; 1 (dipende dalla tipologia strutturale)</li>
</ul>
</li>
</ul>
<p>Il valore di q dipende da:</p>
<ol>
<li>Materiale strutturale (CA, CAP, Acciaio, Legno, Muratura)</li>
<li>Tipologia strutturale</li>
<li>Regolarità in pianta e in altezza</li>
<li>Classe di duttilità</li>
</ol>
<h3>3. Spettro di Progetto</h3>
<p>Lo spettro di progetto Sd(T) si ottiene dallo spettro elastico sostituendo η con 1/q:</p>
<h2>Stato Analisi</h2>
<ul>
<li>[x] Azione sismica 2008.pdf</li>
<li>[x] 2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf</li>
<li>[x] allegati.pdf</li>
</ul>
<h2>Note e Osservazioni</h2>
<ol>
<li>
<p><strong>Differenze Riscontrate</strong>:</p>
<ul>
<li>Il nostro codice attuale non implementa correttamente la formula di interpolazione</li>
<li>I periodi di ritorno calcolati sembrano corretti</li>
<li>La precisione dei risultati è conforme (3 decimali)</li>
</ul>
</li>
<li>
<p><strong>Azioni Necessarie</strong>:</p>
<ul>
<li>Correggere la formula di interpolazione</li>
<li>Aggiungere validazioni sui limiti geografici</li>
<li>Implementare controlli di coerenza sui risultati</li>
</ul>
</li>
<li>
<p><strong>Nuove Differenze Riscontrate</strong>:</p>
<ul>
<li>I coefficienti di amplificazione stratigrafica devono rispettare limiti precisi</li>
<li>Il calcolo di TC e TB deve considerare il coefficiente CC</li>
<li>I coefficienti topografici devono essere applicati correttamente</li>
</ul>
</li>
<li>
<p><strong>Ulteriori Azioni Necessarie</strong>:</p>
<ul>
<li>Implementare i controlli sui limiti dei coefficienti SS</li>
<li>Aggiungere il calcolo corretto di TC e TB</li>
<li>Verificare l'applicazione dei coefficienti topografici</li>
</ul>
</li>
<li>
<p><strong>Ulteriori Differenze Riscontrate</strong>:</p>
<ul>
<li>Il calcolo dello smorzamento η non è implementato</li>
<li>Il fattore di struttura q non è considerato</li>
<li>Lo spettro di progetto non è calcolato</li>
</ul>
</li>
<li>
<p><strong>Azioni Finali Necessarie</strong>:</p>
<ul>
<li>Implementare il calcolo completo dello spettro elastico</li>
<li>Aggiungere il calcolo del fattore η per diversi smorzamenti</li>
<li>Implementare il calcolo dello spettro di progetto con fattore q</li>
<li>Aggiungere validazioni per tutti i parametri di input</li>
</ul>
</li>
</ol>
<p><em>L'analisi di tutti i documenti è completata. Possiamo procedere con l'implementazione delle correzioni necessarie.</em> </p>
<h2>Sviluppi Futuri e Innovazioni</h2>
<p>Il progetto A.S.D.P. è in continua evoluzione, con focus su:</p>
<ul>
<li>Miglioramento degli algoritmi di IA per una più precisa previsione della risposta sismica</li>
<li>Sviluppo di nuovi materiali magnetoreologici con prestazioni superiori</li>
<li>Implementazione di sistemi di monitoraggio in tempo reale</li>
<li>Integrazione con sistemi di early warning sismico</li>
<li>Ottimizzazione dei costi di produzione e installazione</li>
</ul>

        </div>
    </div>

    <button class="print-button" id="printBtn" title="Stampa documento">
        <i class="fas fa-print"></i>
    </button>

    <script>
    $(document).ready(function() {
        // Configurazione Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                curve: 'basis',
                padding: 15,
                useMaxWidth: true,
                htmlLabels: true,
                nodeSpacing: 50,
                rankSpacing: 50
            }
        });

        // Inizializza Mermaid dopo il caricamento della pagina
        setTimeout(function() {
            mermaid.run({
                querySelector: '.mermaid'
            });
        }, 500);
        
        // Rimuovi eventuali pulsanti di chiusura esistenti
        $('.close-button').remove();
        
        // Aggiungi pulsante chiudi dinamicamente solo se non esiste già
        if ($('#closeBtn').length === 0) {
            $('body').append(
                $('<button>')
                    .attr('id', 'closeBtn')
                    .addClass('close-button')
                    .attr('title', 'Chiudi')
                    .html('<i class="fas fa-times"></i>')
                    .on('click', function() {
                        // Verifica se siamo in un iframe
                        if (window !== window.top) {
                            // Accedi direttamente al popup nel parent e chiudilo
                            const frameElement = window.frameElement;
                            if (frameElement) {
                                const popup = frameElement.closest('.doc-popup');
                                if (popup) {
                                    popup.style.display = 'none';
                                }
                            }
                        } else {
                            // Altrimenti chiudi la finestra
                            window.close();
                        }
                    })
            );
        }

        // Gestione link interni
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 20
                }, 500);
                
                // Aggiorna URL senza ricaricare la pagina
                history.pushState(null, null, $(this).attr('href'));
                
                // Evidenzia la sezione attiva
                $('.doc-section').removeClass('active');
                target.addClass('active');
                
                // Evidenzia il link nell'indice
                $('.toc a').removeClass('active');
                $('.toc a[href="' + $(this).attr('href') + '"]').addClass('active');
            }
        });
        
        // Genera indice laterale
        const toc = $('#toc');
        $('.doc-section').each(function() {
            const section = $(this);
            const id = section.attr('id');
            const title = section.find('h3').first().text();
            
            toc.append(
                $('<li>').append(
                    $('<a>')
                        .attr('href', '#' + id)
                        .text(title)
                )
            );
        });
        
        // Evidenzia sezione corrente durante lo scroll
        $(window).scroll(function() {
            const scrollPos = $(window).scrollTop();
            
            $('.doc-section').each(function() {
                const section = $(this);
                const sectionTop = section.offset().top - 100;
                const sectionBottom = sectionTop + section.outerHeight();
                
                if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
                    $('.doc-section').removeClass('active');
                    section.addClass('active');
                    
                    $('.toc a').removeClass('active');
                    $('.toc a[href="#' + section.attr('id') + '"]').addClass('active');
                    
                    // Aggiorna URL senza ricaricare la pagina
                    history.replaceState(null, null, '#' + section.attr('id'));
                }
            });
        });
        
        // Gestione pulsante stampa
        $('#printBtn').on('click', function() {
            window.print();
        });
    });
    </script>
</body>
</html>