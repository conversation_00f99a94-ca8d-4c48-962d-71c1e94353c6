[10:53:15] [+7ms] [SYSTEM] 🚀 SISTEMA DEBUG INIZIALIZZATO
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at ModalDebugLogger.init (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:26:14)
    at new ModalDebugLogger (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:12:14)
    at http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:317:22
================================================================================

[10:53:43] [+27606ms] [CLICK] CLICK INTERCETTATO
DATA: {
  "tagName": "BUTTON",
  "className": "test-btn",
  "id": "",
  "onclick": "testCloseButtonFix()",
  "type": "submit",
  "textContent": "\n            🧪 Test Correzione Pulsante\n        ",
  "parentElement": "DIV",
  "isModalClose": false
}
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at HTMLDocument.<anonymous> (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:165:18)
================================================================================

[10:53:47] [+31834ms] [CLICK] CLICK INTERCETTATO
DATA: {
  "tagName": "BUTTON",
  "className": "modal-close",
  "id": "",
  "onclick": "closeInertialMassModal()",
  "type": "button",
  "textContent": "\n                \n                    \n           ",
  "parentElement": "DIV",
  "isModalClose": true
}
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at HTMLDocument.<anonymous> (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:165:18)
    at http://localhost/progetti/asdp/inertial_mass/test_close_fix.html:197:53
================================================================================

[10:53:47] [+31835ms] [WARNING] 🎯 PULSANTE CHIUSURA CLICCATO!
DATA: {
  "element": "<button type=\"button\" class=\"modal-close\" onclick=\"closeInertialMassModal()\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                </svg>\n            </button>",
  "computedStyle": {
    "accentColor": "",
    "additiveSymbols": "",
    "alignContent": "",
    "alignItems": "",
    "alignSelf": "",
    "alignmentBaseline": "",
    "all": "",
    "anchorName": "",
    "anchorScope": "",
    "animation": "",
    "animationComposition": "",
    "animationDelay": "",
    "animationDirection": "",
    "animationDuration": "",
    "animationFillMode": "",
    "animationIterationCount": "",
    "animationName": "",
    "animationPlayState": "",
    "animationRange": "",
    "animationRangeEnd": "",
    "animationRangeStart": "",
    "animationTimeline": "",
    "animationTimingFunction": "",
    "appRegion": "",
    "appearance": "",
    "ascentOverride": "",
    "aspectRatio": "",
    "backdropFilter": "",
    "backfaceVisibility": "",
    "background": "",
    "backgroundAttachment": "",
    "backgroundBlendMode": "",
    "backgroundClip": "",
    "backgroundColor": "",
    "backgroundImage": "",
    "backgroundOrigin": "",
    "backgroundPosition": "",
    "backgroundPositionX": "",
    "backgroundPositionY": "",
    "backgroundRepeat": "",
    "backgroundSize": "",
    "basePalette": "",
    "baselineShift": "",
    "baselineSource": "",
    "blockSize": "",
    "border": "",
    "borderBlock": "",
    "borderBlockColor": "",
    "borderBlockEnd": "",
    "borderBlockEndColor": "",
    "borderBlockEndStyle": "",
    "borderBlockEndWidth": "",
    "borderBlockStart": "",
    "borderBlockStartColor": "",
    "borderBlockStartStyle": "",
    "borderBlockStartWidth": "",
    "borderBlockStyle": "",
    "borderBlockWidth": "",
    "borderBottom": "",
    "borderBottomColor": "",
    "borderBottomLeftRadius": "",
    "borderBottomRightRadius": "",
    "borderBottomStyle": "",
    "borderBottomWidth": "",
    "borderCollapse": "",
    "borderColor": "",
    "borderEndEndRadius": "",
    "borderEndStartRadius": "",
    "borderImage": "",
    "borderImageOutset": "",
    "borderImageRepeat": "",
    "borderImageSlice": "",
    "borderImageSource": "",
    "borderImageWidth": "",
    "borderInline": "",
    "borderInlineColor": "",
    "borderInlineEnd": "",
    "borderInlineEndColor": "",
    "borderInlineEndStyle": "",
    "borderInlineEndWidth": "",
    "borderInlineStart": "",
    "borderInlineStartColor": "",
    "borderInlineStartStyle": "",
    "borderInlineStartWidth": "",
    "borderInlineStyle": "",
    "borderInlineWidth": "",
    "borderLeft": "",
    "borderLeftColor": "",
    "borderLeftStyle": "",
    "borderLeftWidth": "",
    "borderRadius": "",
    "borderRight": "",
    "borderRightColor": "",
    "borderRightStyle": "",
    "borderRightWidth": "",
    "borderSpacing": "",
    "borderStartEndRadius": "",
    "borderStartStartRadius": "",
    "borderStyle": "",
    "borderTop": "",
    "borderTopColor": "",
    "borderTopLeftRadius": "",
    "borderTopRightRadius": "",
    "borderTopStyle": "",
    "borderTopWidth": "",
    "borderWidth": "",
    "bottom": "",
    "boxDecorationBreak": "",
    "boxShadow": "",
    "boxSizing": "",
    "breakAfter": "",
    "breakBefore": "",
    "breakInside": "",
    "bufferedRendering": "",
    "captionSide": "",
    "caretColor": "",
    "clear": "",
    "clip": "",
    "clipPath": "",
    "clipRule": "",
    "color": "",
    "colorInterpolation": "",
    "colorInterpolationFilters": "",
    "colorRendering": "",
    "colorScheme": "",
    "columnCount": "",
    "columnFill": "",
    "columnGap": "",
    "columnRule": "",
    "columnRuleColor": "",
    "columnRuleStyle": "",
    "columnRuleWidth": "",
    "columnSpan": "",
    "columnWidth": "",
    "columns": "",
    "contain": "",
    "containIntrinsicBlockSize": "",
    "containIntrinsicHeight": "",
    "containIntrinsicInlineSize": "",
    "containIntrinsicSize": "",
    "containIntrinsicWidth": "",
    "container": "",
    "containerName": "",
    "containerType": "",
    "content": "",
    "contentVisibility": "",
    "counterIncrement": "",
    "counterReset": "",
    "counterSet": "",
    "cursor": "",
    "cx": "",
    "cy": "",
    "d": "",
    "descentOverride": "",
    "direction": "",
    "display": "",
    "dominantBaseline": "",
    "dynamicRangeLimit": "",
    "emptyCells": "",
    "fallback": "",
    "fieldSizing": "",
    "fill": "",
    "fillOpacity": "",
    "fillRule": "",
    "filter": "",
    "flex": "",
    "flexBasis": "",
    "flexDirection": "",
    "flexFlow": "",
    "flexGrow": "",
    "flexShrink": "",
    "flexWrap": "",
    "float": "",
    "floodColor": "",
    "floodOpacity": "",
    "font": "",
    "fontDisplay": "",
    "fontFamily": "",
    "fontFeatureSettings": "",
    "fontKerning": "",
    "fontOpticalSizing": "",
    "fontPalette": "",
    "fontSize": "",
    "fontSizeAdjust": "",
    "fontStretch": "",
    "fontStyle": "",
    "fontSynthesis": "",
    "fontSynthesisSmallCaps": "",
    "fontSynthesisStyle": "",
    "fontSynthesisWeight": "",
    "fontVariant": "",
    "fontVariantAlternates": "",
    "fontVariantCaps": "",
    "fontVariantEastAsian": "",
    "fontVariantEmoji": "",
    "fontVariantLigatures": "",
    "fontVariantNumeric": "",
    "fontVariantPosition": "",
    "fontVariationSettings": "",
    "fontWeight": "",
    "forcedColorAdjust": "",
    "gap": "",
    "grid": "",
    "gridArea": "",
    "gridAutoColumns": "",
    "gridAutoFlow": "",
    "gridAutoRows": "",
    "gridColumn": "",
    "gridColumnEnd": "",
    "gridColumnGap": "",
    "gridColumnStart": "",
    "gridGap": "",
    "gridRow": "",
    "gridRowEnd": "",
    "gridRowGap": "",
    "gridRowStart": "",
    "gridTemplate": "",
    "gridTemplateAreas": "",
    "gridTemplateColumns": "",
    "gridTemplateRows": "",
    "height": "",
    "hyphenateCharacter": "",
    "hyphenateLimitChars": "",
    "hyphens": "",
    "imageOrientation": "",
    "imageRendering": "",
    "inherits": "",
    "initialLetter": "",
    "initialValue": "",
    "inlineSize": "",
    "inset": "",
    "insetBlock": "",
    "insetBlockEnd": "",
    "insetBlockStart": "",
    "insetInline": "",
    "insetInlineEnd": "",
    "insetInlineStart": "",
    "interactivity": "",
    "interpolateSize": "",
    "isolation": "",
    "justifyContent": "",
    "justifyItems": "",
    "justifySelf": "",
    "left": "",
    "letterSpacing": "",
    "lightingColor": "",
    "lineBreak": "",
    "lineGapOverride": "",
    "lineHeight": "",
    "listStyle": "",
    "listStyleImage": "",
    "listStylePosition": "",
    "listStyleType": "",
    "margin": "",
    "marginBlock": "",
    "marginBlockEnd": "",
    "marginBlockStart": "",
    "marginBottom": "",
    "marginInline": "",
    "marginInlineEnd": "",
    "marginInlineStart": "",
    "marginLeft": "",
    "marginRight": "",
    "marginTop": "",
    "marker": "",
    "markerEnd": "",
    "markerMid": "",
    "markerStart": "",
    "mask": "",
    "maskClip": "",
    "maskComposite": "",
    "maskImage": "",
    "maskMode": "",
    "maskOrigin": "",
    "maskPosition": "",
    "maskRepeat": "",
    "maskSize": "",
    "maskType": "",
    "mathDepth": "",
    "mathShift": "",
    "mathStyle": "",
    "maxBlockSize": "",
    "maxHeight": "",
    "maxInlineSize": "",
    "maxWidth": "",
    "minBlockSize": "",
    "minHeight": "",
    "minInlineSize": "",
    "minWidth": "",
    "mixBlendMode": "",
    "navigation": "",
    "negative": "",
    "objectFit": "",
    "objectPosition": "",
    "objectViewBox": "",
    "offset": "",
    "offsetAnchor": "",
    "offsetDistance": "",
    "offsetPath": "",
    "offsetPosition": "",
    "offsetRotate": "",
    "opacity": "",
    "order": "",
    "orphans": "",
    "outline": "",
    "outlineColor": "",
    "outlineOffset": "",
    "outlineStyle": "",
    "outlineWidth": "",
    "overflow": "",
    "overflowAnchor": "",
    "overflowBlock": "",
    "overflowClipMargin": "",
    "overflowInline": "",
    "overflowWrap": "",
    "overflowX": "",
    "overflowY": "",
    "overlay": "",
    "overrideColors": "",
    "overscrollBehavior": "",
    "overscrollBehaviorBlock": "",
    "overscrollBehaviorInline": "",
    "overscrollBehaviorX": "",
    "overscrollBehaviorY": "",
    "pad": "",
    "padding": "",
    "paddingBlock": "",
    "paddingBlockEnd": "",
    "paddingBlockStart": "",
    "paddingBottom": "",
    "paddingInline": "",
    "paddingInlineEnd": "",
    "paddingInlineStart": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingTop": "",
    "page": "",
    "pageBreakAfter": "",
    "pageBreakBefore": "",
    "pageBreakInside": "",
    "pageOrientation": "",
    "paintOrder": "",
    "perspective": "",
    "perspectiveOrigin": "",
    "placeContent": "",
    "placeItems": "",
    "placeSelf": "",
    "pointerEvents": "",
    "position": "",
    "positionAnchor": "",
    "positionArea": "",
    "positionTry": "",
    "positionTryFallbacks": "",
    "positionTryOrder": "",
    "positionVisibility": "",
    "prefix": "",
    "printColorAdjust": "",
    "quotes": "",
    "r": "",
    "range": "",
    "readingFlow": "",
    "readingOrder": "",
    "resize": "",
    "right": "",
    "rotate": "",
    "rowGap": "",
    "rubyAlign": "",
    "rubyPosition": "",
    "rx": "",
    "ry": "",
    "scale": "",
    "scrollBehavior": "",
    "scrollInitialTarget": "",
    "scrollMargin": "",
    "scrollMarginBlock": "",
    "scrollMarginBlockEnd": "",
    "scrollMarginBlockStart": "",
    "scrollMarginBottom": "",
    "scrollMarginInline": "",
    "scrollMarginInlineEnd": "",
    "scrollMarginInlineStart": "",
    "scrollMarginLeft": "",
    "scrollMarginRight": "",
    "scrollMarginTop": "",
    "scrollMarkerGroup": "",
    "scrollPadding": "",
    "scrollPaddingBlock": "",
    "scrollPaddingBlockEnd": "",
    "scrollPaddingBlockStart": "",
    "scrollPaddingBottom": "",
    "scrollPaddingInline": "",
    "scrollPaddingInlineEnd": "",
    "scrollPaddingInlineStart": "",
    "scrollPaddingLeft": "",
    "scrollPaddingRight": "",
    "scrollPaddingTop": "",
    "scrollSnapAlign": "",
    "scrollSnapStop": "",
    "scrollSnapType": "",
    "scrollTimeline": "",
    "scrollTimelineAxis": "",
    "scrollTimelineName": "",
    "scrollbarColor": "",
    "scrollbarGutter": "",
    "scrollbarWidth": "",
    "shapeImageThreshold": "",
    "shapeMargin": "",
    "shapeOutside": "",
    "shapeRendering": "",
    "size": "",
    "sizeAdjust": "",
    "speak": "",
    "speakAs": "",
    "src": "",
    "stopColor": "",
    "stopOpacity": "",
    "stroke": "",
    "strokeDasharray": "",
    "strokeDashoffset": "",
    "strokeLinecap": "",
    "strokeLinejoin": "",
    "strokeMiterlimit": "",
    "strokeOpacity": "",
    "strokeWidth": "",
    "suffix": "",
    "symbols": "",
    "syntax": "",
    "system": "",
    "tabSize": "",
    "tableLayout": "",
    "textAlign": "",
    "textAlignLast": "",
    "textAnchor": "",
    "textBox": "",
    "textBoxEdge": "",
    "textBoxTrim": "",
    "textCombineUpright": "",
    "textDecoration": "",
    "textDecorationColor": "",
    "textDecorationLine": "",
    "textDecorationSkipInk": "",
    "textDecorationStyle": "",
    "textDecorationThickness": "",
    "textEmphasis": "",
    "textEmphasisColor": "",
    "textEmphasisPosition": "",
    "textEmphasisStyle": "",
    "textIndent": "",
    "textOrientation": "",
    "textOverflow": "",
    "textRendering": "",
    "textShadow": "",
    "textSizeAdjust": "",
    "textSpacingTrim": "",
    "textTransform": "",
    "textUnderlineOffset": "",
    "textUnderlinePosition": "",
    "textWrap": "",
    "textWrapMode": "",
    "textWrapStyle": "",
    "timelineScope": "",
    "top": "",
    "touchAction": "",
    "transform": "",
    "transformBox": "",
    "transformOrigin": "",
    "transformStyle": "",
    "transition": "",
    "transitionBehavior": "",
    "transitionDelay": "",
    "transitionDuration": "",
    "transitionProperty": "",
    "transitionTimingFunction": "",
    "translate": "",
    "types": "",
    "unicodeBidi": "",
    "unicodeRange": "",
    "userSelect": "",
    "vectorEffect": "",
    "verticalAlign": "",
    "viewTimeline": "",
    "viewTimelineAxis": "",
    "viewTimelineInset": "",
    "viewTimelineName": "",
    "viewTransitionClass": "",
    "viewTransitionName": "",
    "visibility": "",
    "webkitAlignContent": "",
    "webkitAlignItems": "",
    "webkitAlignSelf": "",
    "webkitAnimation": "",
    "webkitAnimationDelay": "",
    "webkitAnimationDirection": "",
    "webkitAnimationDuration": "",
    "webkitAnimationFillMode": "",
    "webkitAnimationIterationCount": "",
    "webkitAnimationName": "",
    "webkitAnimationPlayState": "",
    "webkitAnimationTimingFunction": "",
    "webkitAppRegion": "",
    "webkitAppearance": "",
    "webkitBackfaceVisibility": "",
    "webkitBackgroundClip": "",
    "webkitBackgroundOrigin": "",
    "webkitBackgroundSize": "",
    "webkitBorderAfter": "",
    "webkitBorderAfterColor": "",
    "webkitBorderAfterStyle": "",
    "webkitBorderAfterWidth": "",
    "webkitBorderBefore": "",
    "webkitBorderBeforeColor": "",
    "webkitBorderBeforeStyle": "",
    "webkitBorderBeforeWidth": "",
    "webkitBorderBottomLeftRadius": "",
    "webkitBorderBottomRightRadius": "",
    "webkitBorderEnd": "",
    "webkitBorderEndColor": "",
    "webkitBorderEndStyle": "",
    "webkitBorderEndWidth": "",
    "webkitBorderHorizontalSpacing": "",
    "webkitBorderImage": "",
    "webkitBorderRadius": "",
    "webkitBorderStart": "",
    "webkitBorderStartColor": "",
    "webkitBorderStartStyle": "",
    "webkitBorderStartWidth": "",
    "webkitBorderTopLeftRadius": "",
    "webkitBorderTopRightRadius": "",
    "webkitBorderVerticalSpacing": "",
    "webkitBoxAlign": "",
    "webkitBoxDecorationBreak": "",
    "webkitBoxDirection": "",
    "webkitBoxFlex": "",
    "webkitBoxOrdinalGroup": "",
    "webkitBoxOrient": "",
    "webkitBoxPack": "",
    "webkitBoxReflect": "",
    "webkitBoxShadow": "",
    "webkitBoxSizing": "",
    "webkitClipPath": "",
    "webkitColumnBreakAfter": "",
    "webkitColumnBreakBefore": "",
    "webkitColumnBreakInside": "",
    "webkitColumnCount": "",
    "webkitColumnGap": "",
    "webkitColumnRule": "",
    "webkitColumnRuleColor": "",
    "webkitColumnRuleStyle": "",
    "webkitColumnRuleWidth": "",
    "webkitColumnSpan": "",
    "webkitColumnWidth": "",
    "webkitColumns": "",
    "webkitFilter": "",
    "webkitFlex": "",
    "webkitFlexBasis": "",
    "webkitFlexDirection": "",
    "webkitFlexFlow": "",
    "webkitFlexGrow": "",
    "webkitFlexShrink": "",
    "webkitFlexWrap": "",
    "webkitFontFeatureSettings": "",
    "webkitFontSmoothing": "",
    "webkitHyphenateCharacter": "",
    "webkitJustifyContent": "",
    "webkitLineBreak": "",
    "webkitLineClamp": "",
    "webkitLocale": "",
    "webkitLogicalHeight": "",
    "webkitLogicalWidth": "",
    "webkitMarginAfter": "",
    "webkitMarginBefore": "",
    "webkitMarginEnd": "",
    "webkitMarginStart": "",
    "webkitMask": "",
    "webkitMaskBoxImage": "",
    "webkitMaskBoxImageOutset": "",
    "webkitMaskBoxImageRepeat": "",
    "webkitMaskBoxImageSlice": "",
    "webkitMaskBoxImageSource": "",
    "webkitMaskBoxImageWidth": "",
    "webkitMaskClip": "",
    "webkitMaskComposite": "",
    "webkitMaskImage": "",
    "webkitMaskOrigin": "",
    "webkitMaskPosition": "",
    "webkitMaskPositionX": "",
    "webkitMaskPositionY": "",
    "webkitMaskRepeat": "",
    "webkitMaskSize": "",
    "webkitMaxLogicalHeight": "",
    "webkitMaxLogicalWidth": "",
    "webkitMinLogicalHeight": "",
    "webkitMinLogicalWidth": "",
    "webkitOpacity": "",
    "webkitOrder": "",
    "webkitPaddingAfter": "",
    "webkitPaddingBefore": "",
    "webkitPaddingEnd": "",
    "webkitPaddingStart": "",
    "webkitPerspective": "",
    "webkitPerspectiveOrigin": "",
    "webkitPerspectiveOriginX": "",
    "webkitPerspectiveOriginY": "",
    "webkitPrintColorAdjust": "",
    "webkitRtlOrdering": "",
    "webkitRubyPosition": "",
    "webkitShapeImageThreshold": "",
    "webkitShapeMargin": "",
    "webkitShapeOutside": "",
    "webkitTapHighlightColor": "",
    "webkitTextCombine": "",
    "webkitTextDecorationsInEffect": "",
    "webkitTextEmphasis": "",
    "webkitTextEmphasisColor": "",
    "webkitTextEmphasisPosition": "",
    "webkitTextEmphasisStyle": "",
    "webkitTextFillColor": "",
    "webkitTextOrientation": "",
    "webkitTextSecurity": "",
    "webkitTextSizeAdjust": "",
    "webkitTextStroke": "",
    "webkitTextStrokeColor": "",
    "webkitTextStrokeWidth": "",
    "webkitTransform": "",
    "webkitTransformOrigin": "",
    "webkitTransformOriginX": "",
    "webkitTransformOriginY": "",
    "webkitTransformOriginZ": "",
    "webkitTransformStyle": "",
    "webkitTransition": "",
    "webkitTransitionDelay": "",
    "webkitTransitionDuration": "",
    "webkitTransitionProperty": "",
    "webkitTransitionTimingFunction": "",
    "webkitUserDrag": "",
    "webkitUserModify": "",
    "webkitUserSelect": "",
    "webkitWritingMode": "",
    "whiteSpace": "",
    "whiteSpaceCollapse": "",
    "widows": "",
    "width": "",
    "willChange": "",
    "wordBreak": "",
    "wordSpacing": "",
    "wordWrap": "",
    "writingMode": "",
    "x": "",
    "y": "",
    "zIndex": "",
    "zoom": ""
  },
  "eventListeners": null
}
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at HTMLDocument.<anonymous> (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:178:22)
    at http://localhost/progetti/asdp/inertial_mass/test_close_fix.html:197:53
================================================================================

[10:54:05] [+49697ms] [CLICK] CLICK INTERCETTATO
DATA: {
  "tagName": "BUTTON",
  "className": "test-btn",
  "id": "",
  "onclick": "analyzeModal()",
  "type": "submit",
  "textContent": "\n            🔍 Analizza Modal\n        ",
  "parentElement": "DIV",
  "isModalClose": false
}
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at HTMLDocument.<anonymous> (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:165:18)
================================================================================

[10:54:05] [+49699ms] [INFO] 🔍 ANALISI STATO MODAL
DATA: {
  "modalExists": true,
  "display": "none",
  "visibility": "visible",
  "opacity": "1",
  "zIndex": "99999",
  "position": "fixed",
  "closeButtonsFound": 2,
  "closeButtonsDetails": [
    {
      "tagName": "BUTTON",
      "className": "modal-close",
      "onclick": "closeInertialMassModal()",
      "disabled": false,
      "style": ""
    },
    {
      "tagName": "BUTTON",
      "className": "btn btn-primary",
      "onclick": "closeInertialMassModal()",
      "disabled": false,
      "style": ""
    }
  ]
}
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at ModalDebugLogger.analyzeModalState (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:297:14)
    at window.analyzeModal (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:320:48)
    at HTMLButtonElement.onclick (http://localhost/progetti/asdp/inertial_mass/test_close_fix.html:100:94)
================================================================================

[10:54:08] [+52674ms] [CLICK] CLICK INTERCETTATO
DATA: {
  "tagName": "BUTTON",
  "className": "test-btn",
  "id": "",
  "onclick": "testCloseButtonFix()",
  "type": "submit",
  "textContent": "\n            🧪 Test Correzione Pulsante\n        ",
  "parentElement": "DIV",
  "isModalClose": false
}
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at HTMLDocument.<anonymous> (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:165:18)
================================================================================

[10:54:13] [+57392ms] [CLICK] CLICK INTERCETTATO
DATA: {
  "tagName": "BUTTON",
  "className": "modal-close",
  "id": "",
  "onclick": "closeInertialMassModal()",
  "type": "button",
  "textContent": "\n                \n                    \n           ",
  "parentElement": "DIV",
  "isModalClose": true
}
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at HTMLDocument.<anonymous> (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:165:18)
    at http://localhost/progetti/asdp/inertial_mass/test_close_fix.html:197:53
================================================================================

[10:54:13] [+57393ms] [WARNING] 🎯 PULSANTE CHIUSURA CLICCATO!
DATA: {
  "element": "<button type=\"button\" class=\"modal-close\" onclick=\"closeInertialMassModal()\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                </svg>\n            </button>",
  "computedStyle": {
    "0": "accent-color",
    "1": "align-content",
    "2": "align-items",
    "3": "align-self",
    "4": "alignment-baseline",
    "5": "anchor-name",
    "6": "anchor-scope",
    "7": "animation-composition",
    "8": "animation-delay",
    "9": "animation-direction",
    "10": "animation-duration",
    "11": "animation-fill-mode",
    "12": "animation-iteration-count",
    "13": "animation-name",
    "14": "animation-play-state",
    "15": "animation-range-end",
    "16": "animation-range-start",
    "17": "animation-timeline",
    "18": "animation-timing-function",
    "19": "app-region",
    "20": "appearance",
    "21": "backdrop-filter",
    "22": "backface-visibility",
    "23": "background-attachment",
    "24": "background-blend-mode",
    "25": "background-clip",
    "26": "background-color",
    "27": "background-image",
    "28": "background-origin",
    "29": "background-position",
    "30": "background-repeat",
    "31": "background-size",
    "32": "baseline-shift",
    "33": "baseline-source",
    "34": "block-size",
    "35": "border-block-end-color",
    "36": "border-block-end-style",
    "37": "border-block-end-width",
    "38": "border-block-start-color",
    "39": "border-block-start-style",
    "40": "border-block-start-width",
    "41": "border-bottom-color",
    "42": "border-bottom-left-radius",
    "43": "border-bottom-right-radius",
    "44": "border-bottom-style",
    "45": "border-bottom-width",
    "46": "border-collapse",
    "47": "border-end-end-radius",
    "48": "border-end-start-radius",
    "49": "border-image-outset",
    "50": "border-image-repeat",
    "51": "border-image-slice",
    "52": "border-image-source",
    "53": "border-image-width",
    "54": "border-inline-end-color",
    "55": "border-inline-end-style",
    "56": "border-inline-end-width",
    "57": "border-inline-start-color",
    "58": "border-inline-start-style",
    "59": "border-inline-start-width",
    "60": "border-left-color",
    "61": "border-left-style",
    "62": "border-left-width",
    "63": "border-right-color",
    "64": "border-right-style",
    "65": "border-right-width",
    "66": "border-start-end-radius",
    "67": "border-start-start-radius",
    "68": "border-top-color",
    "69": "border-top-left-radius",
    "70": "border-top-right-radius",
    "71": "border-top-style",
    "72": "border-top-width",
    "73": "bottom",
    "74": "box-decoration-break",
    "75": "box-shadow",
    "76": "box-sizing",
    "77": "break-after",
    "78": "break-before",
    "79": "break-inside",
    "80": "buffered-rendering",
    "81": "caption-side",
    "82": "caret-color",
    "83": "clear",
    "84": "clip",
    "85": "clip-path",
    "86": "clip-rule",
    "87": "color",
    "88": "color-interpolation",
    "89": "color-interpolation-filters",
    "90": "color-rendering",
    "91": "column-count",
    "92": "column-gap",
    "93": "column-rule-color",
    "94": "column-rule-style",
    "95": "column-rule-width",
    "96": "column-span",
    "97": "column-width",
    "98": "contain-intrinsic-block-size",
    "99": "contain-intrinsic-height",
    "100": "contain-intrinsic-inline-size",
    "101": "contain-intrinsic-size",
    "102": "contain-intrinsic-width",
    "103": "container-name",
    "104": "container-type",
    "105": "content",
    "106": "cursor",
    "107": "cx",
    "108": "cy",
    "109": "d",
    "110": "direction",
    "111": "display",
    "112": "dominant-baseline",
    "113": "dynamic-range-limit",
    "114": "empty-cells",
    "115": "field-sizing",
    "116": "fill",
    "117": "fill-opacity",
    "118": "fill-rule",
    "119": "filter",
    "120": "flex-basis",
    "121": "flex-direction",
    "122": "flex-grow",
    "123": "flex-shrink",
    "124": "flex-wrap",
    "125": "float",
    "126": "flood-color",
    "127": "flood-opacity",
    "128": "font-family",
    "129": "font-kerning",
    "130": "font-optical-sizing",
    "131": "font-palette",
    "132": "font-size",
    "133": "font-size-adjust",
    "134": "font-stretch",
    "135": "font-style",
    "136": "font-synthesis-small-caps",
    "137": "font-synthesis-style",
    "138": "font-synthesis-weight",
    "139": "font-variant",
    "140": "font-variant-alternates",
    "141": "font-variant-caps",
    "142": "font-variant-east-asian",
    "143": "font-variant-emoji",
    "144": "font-variant-ligatures",
    "145": "font-variant-numeric",
    "146": "font-variant-position",
    "147": "font-weight",
    "148": "grid-auto-columns",
    "149": "grid-auto-flow",
    "150": "grid-auto-rows",
    "151": "grid-column-end",
    "152": "grid-column-start",
    "153": "grid-row-end",
    "154": "grid-row-start",
    "155": "grid-template-areas",
    "156": "grid-template-columns",
    "157": "grid-template-rows",
    "158": "height",
    "159": "hyphenate-character",
    "160": "hyphenate-limit-chars",
    "161": "hyphens",
    "162": "image-orientation",
    "163": "image-rendering",
    "164": "initial-letter",
    "165": "inline-size",
    "166": "inset-block-end",
    "167": "inset-block-start",
    "168": "inset-inline-end",
    "169": "inset-inline-start",
    "170": "interactivity",
    "171": "interpolate-size",
    "172": "isolation",
    "173": "justify-content",
    "174": "justify-items",
    "175": "justify-self",
    "176": "left",
    "177": "letter-spacing",
    "178": "lighting-color",
    "179": "line-break",
    "180": "line-height",
    "181": "list-style-image",
    "182": "list-style-position",
    "183": "list-style-type",
    "184": "margin-block-end",
    "185": "margin-block-start",
    "186": "margin-bottom",
    "187": "margin-inline-end",
    "188": "margin-inline-start",
    "189": "margin-left",
    "190": "margin-right",
    "191": "margin-top",
    "192": "marker-end",
    "193": "marker-mid",
    "194": "marker-start",
    "195": "mask-clip",
    "196": "mask-composite",
    "197": "mask-image",
    "198": "mask-mode",
    "199": "mask-origin",
    "200": "mask-position",
    "201": "mask-repeat",
    "202": "mask-size",
    "203": "mask-type",
    "204": "math-depth",
    "205": "math-shift",
    "206": "math-style",
    "207": "max-block-size",
    "208": "max-height",
    "209": "max-inline-size",
    "210": "max-width",
    "211": "min-block-size",
    "212": "min-height",
    "213": "min-inline-size",
    "214": "min-width",
    "215": "mix-blend-mode",
    "216": "object-fit",
    "217": "object-position",
    "218": "object-view-box",
    "219": "offset-anchor",
    "220": "offset-distance",
    "221": "offset-path",
    "222": "offset-position",
    "223": "offset-rotate",
    "224": "opacity",
    "225": "order",
    "226": "orphans",
    "227": "outline-color",
    "228": "outline-offset",
    "229": "outline-style",
    "230": "outline-width",
    "231": "overflow-anchor",
    "232": "overflow-block",
    "233": "overflow-clip-margin",
    "234": "overflow-inline",
    "235": "overflow-wrap",
    "236": "overflow-x",
    "237": "overflow-y",
    "238": "overlay",
    "239": "overscroll-behavior-block",
    "240": "overscroll-behavior-inline",
    "241": "padding-block-end",
    "242": "padding-block-start",
    "243": "padding-bottom",
    "244": "padding-inline-end",
    "245": "padding-inline-start",
    "246": "padding-left",
    "247": "padding-right",
    "248": "padding-top",
    "249": "paint-order",
    "250": "perspective",
    "251": "perspective-origin",
    "252": "pointer-events",
    "253": "position",
    "254": "position-anchor",
    "255": "position-area",
    "256": "position-try-fallbacks",
    "257": "position-try-order",
    "258": "position-visibility",
    "259": "print-color-adjust",
    "260": "r",
    "261": "reading-flow",
    "262": "reading-order",
    "263": "resize",
    "264": "right",
    "265": "rotate",
    "266": "row-gap",
    "267": "ruby-align",
    "268": "ruby-position",
    "269": "rx",
    "270": "ry",
    "271": "scale",
    "272": "scroll-behavior",
    "273": "scroll-initial-target",
    "274": "scroll-margin-block-end",
    "275": "scroll-margin-block-start",
    "276": "scroll-margin-inline-end",
    "277": "scroll-margin-inline-start",
    "278": "scroll-marker-group",
    "279": "scroll-padding-block-end",
    "280": "scroll-padding-block-start",
    "281": "scroll-padding-inline-end",
    "282": "scroll-padding-inline-start",
    "283": "scroll-timeline-axis",
    "284": "scroll-timeline-name",
    "285": "scrollbar-color",
    "286": "scrollbar-gutter",
    "287": "scrollbar-width",
    "288": "shape-image-threshold",
    "289": "shape-margin",
    "290": "shape-outside",
    "291": "shape-rendering",
    "292": "speak",
    "293": "stop-color",
    "294": "stop-opacity",
    "295": "stroke",
    "296": "stroke-dasharray",
    "297": "stroke-dashoffset",
    "298": "stroke-linecap",
    "299": "stroke-linejoin",
    "300": "stroke-miterlimit",
    "301": "stroke-opacity",
    "302": "stroke-width",
    "303": "tab-size",
    "304": "table-layout",
    "305": "text-align",
    "306": "text-align-last",
    "307": "text-anchor",
    "308": "text-box-edge",
    "309": "text-box-trim",
    "310": "text-decoration",
    "311": "text-decoration-color",
    "312": "text-decoration-line",
    "313": "text-decoration-skip-ink",
    "314": "text-decoration-style",
    "315": "text-emphasis-color",
    "316": "text-emphasis-position",
    "317": "text-emphasis-style",
    "318": "text-indent",
    "319": "text-overflow",
    "320": "text-rendering",
    "321": "text-shadow",
    "322": "text-size-adjust",
    "323": "text-spacing-trim",
    "324": "text-transform",
    "325": "text-underline-position",
    "326": "text-wrap-mode",
    "327": "text-wrap-style",
    "328": "timeline-scope",
    "329": "top",
    "330": "touch-action",
    "331": "transform",
    "332": "transform-origin",
    "333": "transform-style",
    "334": "transition-behavior",
    "335": "transition-delay",
    "336": "transition-duration",
    "337": "transition-property",
    "338": "transition-timing-function",
    "339": "translate",
    "340": "unicode-bidi",
    "341": "user-select",
    "342": "vector-effect",
    "343": "vertical-align",
    "344": "view-timeline-axis",
    "345": "view-timeline-inset",
    "346": "view-timeline-name",
    "347": "view-transition-class",
    "348": "view-transition-name",
    "349": "visibility",
    "350": "white-space-collapse",
    "351": "widows",
    "352": "width",
    "353": "will-change",
    "354": "word-break",
    "355": "word-spacing",
    "356": "writing-mode",
    "357": "x",
    "358": "y",
    "359": "z-index",
    "360": "zoom",
    "361": "-webkit-border-horizontal-spacing",
    "362": "-webkit-border-image",
    "363": "-webkit-border-vertical-spacing",
    "364": "-webkit-box-align",
    "365": "-webkit-box-decoration-break",
    "366": "-webkit-box-direction",
    "367": "-webkit-box-flex",
    "368": "-webkit-box-ordinal-group",
    "369": "-webkit-box-orient",
    "370": "-webkit-box-pack",
    "371": "-webkit-box-reflect",
    "372": "-webkit-font-smoothing",
    "373": "-webkit-line-break",
    "374": "-webkit-line-clamp",
    "375": "-webkit-locale",
    "376": "-webkit-mask-box-image",
    "377": "-webkit-mask-box-image-outset",
    "378": "-webkit-mask-box-image-repeat",
    "379": "-webkit-mask-box-image-slice",
    "380": "-webkit-mask-box-image-source",
    "381": "-webkit-mask-box-image-width",
    "382": "-webkit-rtl-ordering",
    "383": "-webkit-tap-highlight-color",
    "384": "-webkit-text-combine",
    "385": "-webkit-text-decorations-in-effect",
    "386": "-webkit-text-fill-color",
    "387": "-webkit-text-orientation",
    "388": "-webkit-text-security",
    "389": "-webkit-text-stroke-color",
    "390": "-webkit-text-stroke-width",
    "391": "-webkit-user-drag",
    "392": "-webkit-user-modify",
    "393": "-webkit-writing-mode",
    "accentColor": "auto",
    "additiveSymbols": "",
    "alignContent": "normal",
    "alignItems": "normal",
    "alignSelf": "auto",
    "alignmentBaseline": "auto",
    "all": "",
    "anchorName": "none",
    "anchorScope": "none",
    "animation": "none 0s ease 0s 1 normal none running",
    "animationComposition": "replace",
    "animationDelay": "0s",
    "animationDirection": "normal",
    "animationDuration": "0s",
    "animationFillMode": "none",
    "animationIterationCount": "1",
    "animationName": "none",
    "animationPlayState": "running",
    "animationRange": "normal",
    "animationRangeEnd": "normal",
    "animationRangeStart": "normal",
    "animationTimeline": "auto",
    "animationTimingFunction": "ease",
    "appRegion": "none",
    "appearance": "button",
    "ascentOverride": "",
    "aspectRatio": "auto",
    "backdropFilter": "none",
    "backfaceVisibility": "visible",
    "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box",
    "backgroundAttachment": "scroll",
    "backgroundBlendMode": "normal",
    "backgroundClip": "border-box",
    "backgroundColor": "rgba(0, 0, 0, 0)",
    "backgroundImage": "none",
    "backgroundOrigin": "padding-box",
    "backgroundPosition": "0% 0%",
    "backgroundPositionX": "0%",
    "backgroundPositionY": "0%",
    "backgroundRepeat": "repeat",
    "backgroundSize": "auto",
    "basePalette": "",
    "baselineShift": "0px",
    "baselineSource": "auto",
    "blockSize": "auto",
    "border": "0px none rgb(248, 249, 250)",
    "borderBlock": "0px none rgb(248, 249, 250)",
    "borderBlockColor": "rgb(248, 249, 250)",
    "borderBlockEnd": "0px none rgb(248, 249, 250)",
    "borderBlockEndColor": "rgb(248, 249, 250)",
    "borderBlockEndStyle": "none",
    "borderBlockEndWidth": "0px",
    "borderBlockStart": "0px none rgb(248, 249, 250)",
    "borderBlockStartColor": "rgb(248, 249, 250)",
    "borderBlockStartStyle": "none",
    "borderBlockStartWidth": "0px",
    "borderBlockStyle": "none",
    "borderBlockWidth": "0px",
    "borderBottom": "0px none rgb(248, 249, 250)",
    "borderBottomColor": "rgb(248, 249, 250)",
    "borderBottomLeftRadius": "4px",
    "borderBottomRightRadius": "4px",
    "borderBottomStyle": "none",
    "borderBottomWidth": "0px",
    "borderCollapse": "separate",
    "borderColor": "rgb(248, 249, 250)",
    "borderEndEndRadius": "4px",
    "borderEndStartRadius": "4px",
    "borderImage": "none",
    "borderImageOutset": "0",
    "borderImageRepeat": "stretch",
    "borderImageSlice": "100%",
    "borderImageSource": "none",
    "borderImageWidth": "1",
    "borderInline": "0px none rgb(248, 249, 250)",
    "borderInlineColor": "rgb(248, 249, 250)",
    "borderInlineEnd": "0px none rgb(248, 249, 250)",
    "borderInlineEndColor": "rgb(248, 249, 250)",
    "borderInlineEndStyle": "none",
    "borderInlineEndWidth": "0px",
    "borderInlineStart": "0px none rgb(248, 249, 250)",
    "borderInlineStartColor": "rgb(248, 249, 250)",
    "borderInlineStartStyle": "none",
    "borderInlineStartWidth": "0px",
    "borderInlineStyle": "none",
    "borderInlineWidth": "0px",
    "borderLeft": "0px none rgb(248, 249, 250)",
    "borderLeftColor": "rgb(248, 249, 250)",
    "borderLeftStyle": "none",
    "borderLeftWidth": "0px",
    "borderRadius": "4px",
    "borderRight": "0px none rgb(248, 249, 250)",
    "borderRightColor": "rgb(248, 249, 250)",
    "borderRightStyle": "none",
    "borderRightWidth": "0px",
    "borderSpacing": "0px 0px",
    "borderStartEndRadius": "4px",
    "borderStartStartRadius": "4px",
    "borderStyle": "none",
    "borderTop": "0px none rgb(248, 249, 250)",
    "borderTopColor": "rgb(248, 249, 250)",
    "borderTopLeftRadius": "4px",
    "borderTopRightRadius": "4px",
    "borderTopStyle": "none",
    "borderTopWidth": "0px",
    "borderWidth": "0px",
    "bottom": "auto",
    "boxDecorationBreak": "slice",
    "boxShadow": "none",
    "boxSizing": "border-box",
    "breakAfter": "auto",
    "breakBefore": "auto",
    "breakInside": "auto",
    "bufferedRendering": "auto",
    "captionSide": "top",
    "caretColor": "rgb(248, 249, 250)",
    "clear": "none",
    "clip": "auto",
    "clipPath": "none",
    "clipRule": "nonzero",
    "color": "rgb(248, 249, 250)",
    "colorInterpolation": "srgb",
    "colorInterpolationFilters": "linearrgb",
    "colorRendering": "auto",
    "colorScheme": "normal",
    "columnCount": "auto",
    "columnFill": "balance",
    "columnGap": "normal",
    "columnRule": "0px none rgb(248, 249, 250)",
    "columnRuleColor": "rgb(248, 249, 250)",
    "columnRuleStyle": "none",
    "columnRuleWidth": "0px",
    "columnSpan": "none",
    "columnWidth": "auto",
    "columns": "auto auto",
    "contain": "none",
    "containIntrinsicBlockSize": "none",
    "containIntrinsicHeight": "none",
    "containIntrinsicInlineSize": "none",
    "containIntrinsicSize": "none",
    "containIntrinsicWidth": "none",
    "container": "none",
    "containerName": "none",
    "containerType": "normal",
    "content": "normal",
    "contentVisibility": "visible",
    "counterIncrement": "none",
    "counterReset": "none",
    "counterSet": "none",
    "cursor": "pointer",
    "cx": "0px",
    "cy": "0px",
    "d": "none",
    "descentOverride": "",
    "direction": "ltr",
    "display": "block",
    "dominantBaseline": "auto",
    "dynamicRangeLimit": "no-limit",
    "emptyCells": "show",
    "fallback": "",
    "fieldSizing": "fixed",
    "fill": "rgb(0, 0, 0)",
    "fillOpacity": "1",
    "fillRule": "nonzero",
    "filter": "none",
    "flex": "0 1 auto",
    "flexBasis": "auto",
    "flexDirection": "row",
    "flexFlow": "row nowrap",
    "flexGrow": "0",
    "flexShrink": "1",
    "flexWrap": "nowrap",
    "float": "none",
    "floodColor": "rgb(0, 0, 0)",
    "floodOpacity": "1",
    "font": "700 28px / 28px \"Segoe UI\", Arial, sans-serif",
    "fontDisplay": "",
    "fontFamily": "\"Segoe UI\", Arial, sans-serif",
    "fontFeatureSettings": "normal",
    "fontKerning": "auto",
    "fontOpticalSizing": "auto",
    "fontPalette": "normal",
    "fontSize": "28px",
    "fontSizeAdjust": "none",
    "fontStretch": "100%",
    "fontStyle": "normal",
    "fontSynthesis": "weight style small-caps",
    "fontSynthesisSmallCaps": "auto",
    "fontSynthesisStyle": "auto",
    "fontSynthesisWeight": "auto",
    "fontVariant": "normal",
    "fontVariantAlternates": "normal",
    "fontVariantCaps": "normal",
    "fontVariantEastAsian": "normal",
    "fontVariantEmoji": "normal",
    "fontVariantLigatures": "normal",
    "fontVariantNumeric": "normal",
    "fontVariantPosition": "normal",
    "fontVariationSettings": "normal",
    "fontWeight": "700",
    "forcedColorAdjust": "auto",
    "gap": "normal",
    "grid": "none / none / none / row / auto / auto",
    "gridArea": "auto",
    "gridAutoColumns": "auto",
    "gridAutoFlow": "row",
    "gridAutoRows": "auto",
    "gridColumn": "auto",
    "gridColumnEnd": "auto",
    "gridColumnGap": "normal",
    "gridColumnStart": "auto",
    "gridGap": "normal",
    "gridRow": "auto",
    "gridRowEnd": "auto",
    "gridRowGap": "normal",
    "gridRowStart": "auto",
    "gridTemplate": "none",
    "gridTemplateAreas": "none",
    "gridTemplateColumns": "none",
    "gridTemplateRows": "none",
    "height": "auto",
    "hyphenateCharacter": "auto",
    "hyphenateLimitChars": "auto",
    "hyphens": "manual",
    "imageOrientation": "from-image",
    "imageRendering": "auto",
    "inherits": "",
    "initialLetter": "normal",
    "initialValue": "",
    "inlineSize": "auto",
    "inset": "auto",
    "insetBlock": "auto",
    "insetBlockEnd": "auto",
    "insetBlockStart": "auto",
    "insetInline": "auto",
    "insetInlineEnd": "auto",
    "insetInlineStart": "auto",
    "interactivity": "auto",
    "interpolateSize": "numeric-only",
    "isolation": "auto",
    "justifyContent": "normal",
    "justifyItems": "normal",
    "justifySelf": "auto",
    "left": "auto",
    "letterSpacing": "normal",
    "lightingColor": "rgb(255, 255, 255)",
    "lineBreak": "auto",
    "lineGapOverride": "",
    "lineHeight": "28px",
    "listStyle": "outside none disc",
    "listStyleImage": "none",
    "listStylePosition": "outside",
    "listStyleType": "disc",
    "margin": "0px",
    "marginBlock": "0px",
    "marginBlockEnd": "0px",
    "marginBlockStart": "0px",
    "marginBottom": "0px",
    "marginInline": "0px",
    "marginInlineEnd": "0px",
    "marginInlineStart": "0px",
    "marginLeft": "0px",
    "marginRight": "0px",
    "marginTop": "0px",
    "marker": "none",
    "markerEnd": "none",
    "markerMid": "none",
    "markerStart": "none",
    "mask": "none",
    "maskClip": "border-box",
    "maskComposite": "add",
    "maskImage": "none",
    "maskMode": "match-source",
    "maskOrigin": "border-box",
    "maskPosition": "0% 0%",
    "maskRepeat": "repeat",
    "maskSize": "auto",
    "maskType": "luminance",
    "mathDepth": "0",
    "mathShift": "normal",
    "mathStyle": "normal",
    "maxBlockSize": "none",
    "maxHeight": "none",
    "maxInlineSize": "none",
    "maxWidth": "none",
    "minBlockSize": "0px",
    "minHeight": "0px",
    "minInlineSize": "0px",
    "minWidth": "0px",
    "mixBlendMode": "normal",
    "navigation": "",
    "negative": "",
    "objectFit": "fill",
    "objectPosition": "50% 50%",
    "objectViewBox": "none",
    "offset": "none 0px auto 0deg",
    "offsetAnchor": "auto",
    "offsetDistance": "0px",
    "offsetPath": "none",
    "offsetPosition": "normal",
    "offsetRotate": "auto 0deg",
    "opacity": "0.7",
    "order": "0",
    "orphans": "2",
    "outline": "rgb(248, 249, 250) none 0px",
    "outlineColor": "rgb(248, 249, 250)",
    "outlineOffset": "0px",
    "outlineStyle": "none",
    "outlineWidth": "0px",
    "overflow": "visible",
    "overflowAnchor": "auto",
    "overflowBlock": "visible",
    "overflowClipMargin": "0px",
    "overflowInline": "visible",
    "overflowWrap": "normal",
    "overflowX": "visible",
    "overflowY": "visible",
    "overlay": "none",
    "overrideColors": "",
    "overscrollBehavior": "auto",
    "overscrollBehaviorBlock": "auto",
    "overscrollBehaviorInline": "auto",
    "overscrollBehaviorX": "auto",
    "overscrollBehaviorY": "auto",
    "pad": "",
    "padding": "8px",
    "paddingBlock": "8px",
    "paddingBlockEnd": "8px",
    "paddingBlockStart": "8px",
    "paddingBottom": "8px",
    "paddingInline": "8px",
    "paddingInlineEnd": "8px",
    "paddingInlineStart": "8px",
    "paddingLeft": "8px",
    "paddingRight": "8px",
    "paddingTop": "8px",
    "page": "auto",
    "pageBreakAfter": "auto",
    "pageBreakBefore": "auto",
    "pageBreakInside": "auto",
    "pageOrientation": "",
    "paintOrder": "normal",
    "perspective": "none",
    "perspectiveOrigin": "50% 50%",
    "placeContent": "normal",
    "placeItems": "normal",
    "placeSelf": "auto",
    "pointerEvents": "auto",
    "position": "static",
    "positionAnchor": "auto",
    "positionArea": "none",
    "positionTry": "none",
    "positionTryFallbacks": "none",
    "positionTryOrder": "normal",
    "positionVisibility": "always",
    "prefix": "",
    "printColorAdjust": "economy",
    "quotes": "auto",
    "r": "0px",
    "range": "",
    "readingFlow": "normal",
    "readingOrder": "0",
    "resize": "none",
    "right": "auto",
    "rotate": "none",
    "rowGap": "normal",
    "rubyAlign": "space-around",
    "rubyPosition": "over",
    "rx": "auto",
    "ry": "auto",
    "scale": "none",
    "scrollBehavior": "auto",
    "scrollInitialTarget": "none",
    "scrollMargin": "0px",
    "scrollMarginBlock": "0px",
    "scrollMarginBlockEnd": "0px",
    "scrollMarginBlockStart": "0px",
    "scrollMarginBottom": "0px",
    "scrollMarginInline": "0px",
    "scrollMarginInlineEnd": "0px",
    "scrollMarginInlineStart": "0px",
    "scrollMarginLeft": "0px",
    "scrollMarginRight": "0px",
    "scrollMarginTop": "0px",
    "scrollMarkerGroup": "none",
    "scrollPadding": "auto",
    "scrollPaddingBlock": "auto",
    "scrollPaddingBlockEnd": "auto",
    "scrollPaddingBlockStart": "auto",
    "scrollPaddingBottom": "auto",
    "scrollPaddingInline": "auto",
    "scrollPaddingInlineEnd": "auto",
    "scrollPaddingInlineStart": "auto",
    "scrollPaddingLeft": "auto",
    "scrollPaddingRight": "auto",
    "scrollPaddingTop": "auto",
    "scrollSnapAlign": "none",
    "scrollSnapStop": "normal",
    "scrollSnapType": "none",
    "scrollTimeline": "none",
    "scrollTimelineAxis": "block",
    "scrollTimelineName": "none",
    "scrollbarColor": "auto",
    "scrollbarGutter": "auto",
    "scrollbarWidth": "auto",
    "shapeImageThreshold": "0",
    "shapeMargin": "0px",
    "shapeOutside": "none",
    "shapeRendering": "auto",
    "size": "",
    "sizeAdjust": "",
    "speak": "normal",
    "speakAs": "",
    "src": "",
    "stopColor": "rgb(0, 0, 0)",
    "stopOpacity": "1",
    "stroke": "none",
    "strokeDasharray": "none",
    "strokeDashoffset": "0px",
    "strokeLinecap": "butt",
    "strokeLinejoin": "miter",
    "strokeMiterlimit": "4",
    "strokeOpacity": "1",
    "strokeWidth": "1px",
    "suffix": "",
    "symbols": "",
    "syntax": "",
    "system": "",
    "tabSize": "8",
    "tableLayout": "auto",
    "textAlign": "center",
    "textAlignLast": "auto",
    "textAnchor": "start",
    "textBox": "normal",
    "textBoxEdge": "auto",
    "textBoxTrim": "none",
    "textCombineUpright": "none",
    "textDecoration": "none solid rgb(248, 249, 250)",
    "textDecorationColor": "rgb(248, 249, 250)",
    "textDecorationLine": "none",
    "textDecorationSkipInk": "auto",
    "textDecorationStyle": "solid",
    "textDecorationThickness": "auto",
    "textEmphasis": "none rgb(248, 249, 250)",
    "textEmphasisColor": "rgb(248, 249, 250)",
    "textEmphasisPosition": "over",
    "textEmphasisStyle": "none",
    "textIndent": "0px",
    "textOrientation": "mixed",
    "textOverflow": "clip",
    "textRendering": "auto",
    "textShadow": "rgb(0, 0, 0) 0px 1px 0px",
    "textSizeAdjust": "100%",
    "textSpacingTrim": "normal",
    "textTransform": "none",
    "textUnderlineOffset": "auto",
    "textUnderlinePosition": "auto",
    "textWrap": "wrap",
    "textWrapMode": "wrap",
    "textWrapStyle": "auto",
    "timelineScope": "none",
    "top": "auto",
    "touchAction": "auto",
    "transform": "none",
    "transformBox": "view-box",
    "transformOrigin": "50% 50%",
    "transformStyle": "flat",
    "transition": "0.2s",
    "transitionBehavior": "normal",
    "transitionDelay": "0s",
    "transitionDuration": "0.2s",
    "transitionProperty": "all",
    "transitionTimingFunction": "ease",
    "translate": "none",
    "types": "",
    "unicodeBidi": "normal",
    "unicodeRange": "",
    "userSelect": "auto",
    "vectorEffect": "none",
    "verticalAlign": "baseline",
    "viewTimeline": "none",
    "viewTimelineAxis": "block",
    "viewTimelineInset": "auto",
    "viewTimelineName": "none",
    "viewTransitionClass": "none",
    "viewTransitionName": "none",
    "visibility": "visible",
    "webkitAlignContent": "normal",
    "webkitAlignItems": "normal",
    "webkitAlignSelf": "auto",
    "webkitAnimation": "none 0s ease 0s 1 normal none running",
    "webkitAnimationDelay": "0s",
    "webkitAnimationDirection": "normal",
    "webkitAnimationDuration": "0s",
    "webkitAnimationFillMode": "none",
    "webkitAnimationIterationCount": "1",
    "webkitAnimationName": "none",
    "webkitAnimationPlayState": "running",
    "webkitAnimationTimingFunction": "ease",
    "webkitAppRegion": "none",
    "webkitAppearance": "button",
    "webkitBackfaceVisibility": "visible",
    "webkitBackgroundClip": "border-box",
    "webkitBackgroundOrigin": "padding-box",
    "webkitBackgroundSize": "auto",
    "webkitBorderAfter": "0px none rgb(248, 249, 250)",
    "webkitBorderAfterColor": "rgb(248, 249, 250)",
    "webkitBorderAfterStyle": "none",
    "webkitBorderAfterWidth": "0px",
    "webkitBorderBefore": "0px none rgb(248, 249, 250)",
    "webkitBorderBeforeColor": "rgb(248, 249, 250)",
    "webkitBorderBeforeStyle": "none",
    "webkitBorderBeforeWidth": "0px",
    "webkitBorderBottomLeftRadius": "4px",
    "webkitBorderBottomRightRadius": "4px",
    "webkitBorderEnd": "0px none rgb(248, 249, 250)",
    "webkitBorderEndColor": "rgb(248, 249, 250)",
    "webkitBorderEndStyle": "none",
    "webkitBorderEndWidth": "0px",
    "webkitBorderHorizontalSpacing": "0px",
    "webkitBorderImage": "none",
    "webkitBorderRadius": "4px",
    "webkitBorderStart": "0px none rgb(248, 249, 250)",
    "webkitBorderStartColor": "rgb(248, 249, 250)",
    "webkitBorderStartStyle": "none",
    "webkitBorderStartWidth": "0px",
    "webkitBorderTopLeftRadius": "4px",
    "webkitBorderTopRightRadius": "4px",
    "webkitBorderVerticalSpacing": "0px",
    "webkitBoxAlign": "stretch",
    "webkitBoxDecorationBreak": "slice",
    "webkitBoxDirection": "normal",
    "webkitBoxFlex": "0",
    "webkitBoxOrdinalGroup": "1",
    "webkitBoxOrient": "horizontal",
    "webkitBoxPack": "start",
    "webkitBoxReflect": "none",
    "webkitBoxShadow": "none",
    "webkitBoxSizing": "border-box",
    "webkitClipPath": "none",
    "webkitColumnBreakAfter": "auto",
    "webkitColumnBreakBefore": "auto",
    "webkitColumnBreakInside": "auto",
    "webkitColumnCount": "auto",
    "webkitColumnGap": "normal",
    "webkitColumnRule": "0px none rgb(248, 249, 250)",
    "webkitColumnRuleColor": "rgb(248, 249, 250)",
    "webkitColumnRuleStyle": "none",
    "webkitColumnRuleWidth": "0px",
    "webkitColumnSpan": "none",
    "webkitColumnWidth": "auto",
    "webkitColumns": "auto auto",
    "webkitFilter": "none",
    "webkitFlex": "0 1 auto",
    "webkitFlexBasis": "auto",
    "webkitFlexDirection": "row",
    "webkitFlexFlow": "row nowrap",
    "webkitFlexGrow": "0",
    "webkitFlexShrink": "1",
    "webkitFlexWrap": "nowrap",
    "webkitFontFeatureSettings": "normal",
    "webkitFontSmoothing": "auto",
    "webkitHyphenateCharacter": "auto",
    "webkitJustifyContent": "normal",
    "webkitLineBreak": "auto",
    "webkitLineClamp": "none",
    "webkitLocale": "\"it\"",
    "webkitLogicalHeight": "auto",
    "webkitLogicalWidth": "auto",
    "webkitMarginAfter": "0px",
    "webkitMarginBefore": "0px",
    "webkitMarginEnd": "0px",
    "webkitMarginStart": "0px",
    "webkitMask": "none",
    "webkitMaskBoxImage": "none",
    "webkitMaskBoxImageOutset": "0",
    "webkitMaskBoxImageRepeat": "stretch",
    "webkitMaskBoxImageSlice": "0 fill",
    "webkitMaskBoxImageSource": "none",
    "webkitMaskBoxImageWidth": "auto",
    "webkitMaskClip": "border-box",
    "webkitMaskComposite": "add",
    "webkitMaskImage": "none",
    "webkitMaskOrigin": "border-box",
    "webkitMaskPosition": "0% 0%",
    "webkitMaskPositionX": "0%",
    "webkitMaskPositionY": "0%",
    "webkitMaskRepeat": "repeat",
    "webkitMaskSize": "auto",
    "webkitMaxLogicalHeight": "none",
    "webkitMaxLogicalWidth": "none",
    "webkitMinLogicalHeight": "0px",
    "webkitMinLogicalWidth": "0px",
    "webkitOpacity": "0.7",
    "webkitOrder": "0",
    "webkitPaddingAfter": "8px",
    "webkitPaddingBefore": "8px",
    "webkitPaddingEnd": "8px",
    "webkitPaddingStart": "8px",
    "webkitPerspective": "none",
    "webkitPerspectiveOrigin": "50% 50%",
    "webkitPerspectiveOriginX": "",
    "webkitPerspectiveOriginY": "",
    "webkitPrintColorAdjust": "economy",
    "webkitRtlOrdering": "logical",
    "webkitRubyPosition": "before",
    "webkitShapeImageThreshold": "0",
    "webkitShapeMargin": "0px",
    "webkitShapeOutside": "none",
    "webkitTapHighlightColor": "rgba(0, 0, 0, 0)",
    "webkitTextCombine": "none",
    "webkitTextDecorationsInEffect": "none",
    "webkitTextEmphasis": "none rgb(248, 249, 250)",
    "webkitTextEmphasisColor": "rgb(248, 249, 250)",
    "webkitTextEmphasisPosition": "over",
    "webkitTextEmphasisStyle": "none",
    "webkitTextFillColor": "rgb(248, 249, 250)",
    "webkitTextOrientation": "vertical-right",
    "webkitTextSecurity": "none",
    "webkitTextSizeAdjust": "100%",
    "webkitTextStroke": "0px rgb(248, 249, 250)",
    "webkitTextStrokeColor": "rgb(248, 249, 250)",
    "webkitTextStrokeWidth": "0px",
    "webkitTransform": "none",
    "webkitTransformOrigin": "50% 50%",
    "webkitTransformOriginX": "",
    "webkitTransformOriginY": "",
    "webkitTransformOriginZ": "",
    "webkitTransformStyle": "flat",
    "webkitTransition": "0.2s",
    "webkitTransitionDelay": "0s",
    "webkitTransitionDuration": "0.2s",
    "webkitTransitionProperty": "all",
    "webkitTransitionTimingFunction": "ease",
    "webkitUserDrag": "auto",
    "webkitUserModify": "read-only",
    "webkitUserSelect": "auto",
    "webkitWritingMode": "horizontal-tb",
    "whiteSpace": "normal",
    "whiteSpaceCollapse": "collapse",
    "widows": "2",
    "width": "auto",
    "willChange": "auto",
    "wordBreak": "normal",
    "wordSpacing": "0px",
    "wordWrap": "normal",
    "writingMode": "horizontal-tb",
    "x": "0px",
    "y": "0px",
    "zIndex": "auto",
    "zoom": "1"
  },
  "eventListeners": null
}
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at HTMLDocument.<anonymous> (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:178:22)
    at http://localhost/progetti/asdp/inertial_mass/test_close_fix.html:197:53
================================================================================

[10:54:24] [+68656ms] [CLICK] CLICK INTERCETTATO
DATA: {
  "tagName": "BUTTON",
  "className": "test-btn",
  "id": "",
  "onclick": "testCloseButtonFix()",
  "type": "submit",
  "textContent": "\n            🧪 Test Correzione Pulsante\n        ",
  "parentElement": "DIV",
  "isModalClose": false
}
STACK: Error
    at ModalDebugLogger.log (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:89:20)
    at HTMLDocument.<anonymous> (http://localhost/progetti/asdp/inertial_mass/logs/debug_close_button.js:165:18)
================================================================================
