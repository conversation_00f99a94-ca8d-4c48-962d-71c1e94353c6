/**
 * test_console.js - Script di test da eseguire nella console del browser
 * Path: /inertial_mass/test_console.js
 * 
 * Copia e incolla questo codice nella console del browser per testare il modal
 */

// Test completo del modal massa inerziale
function testInertialMassModal() {
    console.log('🧪 === TEST MODAL MASSA INERZIALE ===');
    
    // 1. Verifica presenza del pulsante
    const button = document.querySelector('.inertial-mass-btn');
    if (!button) {
        console.error('❌ Pulsante massa inerziale non trovato!');
        console.log('💡 Suggerimento: Assicurati di essere nella sezione parametri sismici');
        return;
    }
    console.log('✅ Pulsante massa inerziale trovato');
    
    // 2. Simula click del pulsante
    console.log('🖱️ Simulando click del pulsante...');
    button.click();
    
    // 3. Attendi caricamento e verifica modal
    setTimeout(() => {
        const modal = document.getElementById('inertialMassModal');
        if (!modal) {
            console.error('❌ Modal non trovato dopo il click!');
            return;
        }
        
        console.log('✅ Modal trovato nel DOM');
        
        // 4. Verifica visibilità
        const isVisible = modal.style.display === 'flex' || 
                         window.getComputedStyle(modal).display === 'flex';
        
        if (!isVisible) {
            console.error('❌ Modal non è visibile!');
            console.log('🔧 Tentativo di forzare visibilità...');
            if (typeof window.forceModalVisibility === 'function') {
                window.forceModalVisibility();
            }
            return;
        }
        
        console.log('✅ Modal è visibile');
        
        // 5. Verifica elementi del form
        const formElements = modal.querySelectorAll('input, select, label, button');
        console.log(`🔍 Trovati ${formElements.length} elementi form`);
        
        let visibleElements = 0;
        let invisibleElements = 0;
        
        formElements.forEach((element, index) => {
            const styles = window.getComputedStyle(element);
            const isElementVisible = styles.display !== 'none' && 
                                   styles.visibility !== 'hidden' && 
                                   parseFloat(styles.opacity) > 0;
            
            if (isElementVisible) {
                visibleElements++;
            } else {
                invisibleElements++;
                console.warn(`⚠️ Elemento ${index + 1} (${element.tagName}) non visibile:`, {
                    display: styles.display,
                    visibility: styles.visibility,
                    opacity: styles.opacity
                });
            }
        });
        
        console.log(`✅ Elementi visibili: ${visibleElements}`);
        console.log(`❌ Elementi invisibili: ${invisibleElements}`);
        
        // 6. Test interazione
        const categorySelect = modal.querySelector('#im-construction-category');
        if (categorySelect) {
            console.log('🎯 Test interazione con select categoria...');
            categorySelect.focus();
            
            // Simula cambio valore
            categorySelect.value = 'building';
            categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
            console.log('✅ Test interazione completato');
        }
        
        // 7. Riepilogo
        if (invisibleElements === 0) {
            console.log('🎉 SUCCESSO: Tutti gli elementi sono visibili!');
        } else {
            console.log('⚠️ ATTENZIONE: Alcuni elementi non sono visibili');
            console.log('🔧 Prova a eseguire: forceModalVisibility()');
        }
        
        // 8. Chiudi modal dopo 5 secondi
        setTimeout(() => {
            console.log('🔒 Chiusura automatica modal...');
            if (typeof window.closeInertialMassModal === 'function') {
                window.closeInertialMassModal();
            } else {
                modal.style.display = 'none';
            }
            console.log('✅ Test completato');
        }, 5000);
        
    }, 2000); // Attendi 2 secondi per il caricamento
}

// Test rapido della visibilità
function quickVisibilityTest() {
    const modal = document.getElementById('inertialMassModal');
    if (!modal) {
        console.log('❌ Modal non presente nel DOM');
        return;
    }
    
    const isVisible = modal.style.display === 'flex';
    console.log(`Modal visibile: ${isVisible ? '✅' : '❌'}`);
    
    if (isVisible) {
        const formElements = modal.querySelectorAll('input, select, label, button');
        let visible = 0;
        formElements.forEach(el => {
            const styles = window.getComputedStyle(el);
            if (styles.display !== 'none' && styles.visibility !== 'hidden' && parseFloat(styles.opacity) > 0) {
                visible++;
            }
        });
        console.log(`Elementi form visibili: ${visible}/${formElements.length}`);
    }
}

// Forza apertura modal per test
function forceOpenModal() {
    console.log('🔓 Forzando apertura modal...');
    
    // Simula dati sismici di test
    const testSeismicData = {
        lat: 41.9028,
        lon: 12.4964,
        zone: '3',
        ag: 0.062,
        F0: 2.604,
        TC: 0.268,
        soil_category: 'C',
        damping: 0.05,
        q0_factor: 3.0
    };
    
    if (typeof window.openInertialMassModal === 'function') {
        window.openInertialMassModal(testSeismicData);
    } else if (typeof window.initInertialMassModal === 'function') {
        window.initInertialMassModal(testSeismicData);
    } else {
        console.error('❌ Funzioni di apertura modal non disponibili');
        console.log('💡 Prova prima a cliccare il pulsante "Calcolo Massa Inerziale"');
    }
}

// Esporta funzioni per uso globale
window.testInertialMassModal = testInertialMassModal;
window.quickVisibilityTest = quickVisibilityTest;
window.forceOpenModal = forceOpenModal;

console.log(`
🧪 === SCRIPT DI TEST MODAL MASSA INERZIALE ===

Funzioni disponibili:
- testInertialMassModal()     // Test completo automatico
- quickVisibilityTest()       // Test rapido visibilità
- forceOpenModal()           // Forza apertura modal
- debugModalVisibility()     // Debug dettagliato (se disponibile)
- forceModalVisibility()     // Forza visibilità (se disponibile)

Per iniziare il test automatico, esegui:
testInertialMassModal()
`);
