/**
 * modal.css - Stili per la finestra modale massa inerziale
 * Path: /inertial_mass/assets/css/modal.css
 *
 * IMPORTANTE: Questo CSS usa specificità massima per evitare conflitti
 * con i CSS del progetto principale ASDP
 */

/* REGOLA DI EMERGENZA - Forza la visibilità di tutti gli elementi del modal */
#inertialMassModal * {
    visibility: visible !important;
    opacity: 1 !important;
}

/* RIMOSSO #inertialMassModal { display: flex !important; } CHE IMPEDIVA LA CHIUSURA */

#inertialMassModal .modal-container {
    display: flex !important;
    flex-direction: column !important;
}

#inertialMassModal .modal-body {
    display: block !important;
}

#inertialMassModal .form-section {
    display: block !important;
    margin-bottom: 2rem !important;
}

#inertialMassModal .form-row {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
    margin-bottom: 1rem !important;
}

#inertialMassModal .form-group {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
}

#inertialMassModal .info-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
}

#inertialMassModal .info-item {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.25rem !important;
}

/* Overlay modale - Specificità massima per evitare conflitti */
#inertialMassModal.modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.7) !important;
    /* RIMOSSO display: flex !important; CHE IMPEDIVA LA CHIUSURA */
    align-items: center !important;
    justify-content: center !important;
    z-index: 99999 !important; /* Aumentato z-index */
    backdrop-filter: blur(4px) !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
}

/* Stile per il contenitore principale della finestra modale - Specificità massima */
#inertialMassModal .modal-container {
    background-color: #1E1E1E !important; /* Sfondo grigio scuro leggermente più chiaro */
    color: #f8f9fa !important;           /* Testo chiaro per leggibilità */
    font-family: 'Segoe UI', Arial, sans-serif !important; /* Aggiunto font-family */
    border: 1px solid #555e67 !important; /* Bordo per definire i limiti della modale */
    border-radius: 0.5rem !important;     /* Angoli leggermente arrotondati */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6) !important; /* Ombra per dare profondità */
    display: flex !important;
    flex-direction: column !important;
    width: 98% !important;             /* Larghezza quasi pieno schermo */
    max-width: 1400px !important;     /* Larghezza massima molto aumentata */
    max-height: 95vh !important;      /* Altezza massima aumentata */
    margin: auto !important; /* Aggiunto per aiutare con la centratura se l'overlay è flex */
    position: relative !important;
    overflow: hidden !important; /* Previene overflow del contenuto */
}

/* Stile per l'intestazione della modale - Specificità massima */
#inertialMassModal .modal-header {
    padding: 1.25rem 1.5rem !important; /* Aumentato padding verticale */
    border-bottom: 1px solid #555e67 !important; /* Linea di separazione dall'area corpo */
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    background-color: #2C2C2C !important; /* Sfondo header leggermente diverso */
    color: #f8f9fa !important;
    border-radius: 0.5rem 0.5rem 0 0 !important; /* Arrotonda solo gli angoli superiori */
    flex-shrink: 0 !important; /* Impedisce al header di ridursi */
}

#inertialMassModal .modal-header h2,
#inertialMassModal .modal-header .modal-title {
    margin: 0 !important;
    line-height: 1.5 !important;
    font-size: 1.6rem !important; /* Leggermente più grande */
    color: #f8f9fa !important; /* Assicura che il titolo sia chiaro */
    font-family: 'Segoe UI', Arial, sans-serif !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px !important; /* Migliora la leggibilità */
}

#inertialMassModal .modal-header .close-modal-btn,
#inertialMassModal .modal-header .modal-close {
    padding: 0.5rem !important; /* Aumentato per area di click più grande */
    margin: 0 !important;
    background-color: transparent !important;
    border: 0 !important;
    font-size: 1.75rem !important; /* Leggermente più grande */
    font-weight: 700 !important;
    line-height: 1 !important;
    color: #f8f9fa !important;
    text-shadow: 0 1px 0 #000 !important;
    opacity: .7 !important;
    cursor: pointer !important;
    border-radius: 0.25rem !important;
    transition: all 0.2s ease !important;
}

#inertialMassModal .modal-header .close-modal-btn:hover,
#inertialMassModal .modal-header .modal-close:hover {
    opacity: 1 !important;
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Stile per il corpo della modale (dove c'è il form) - Specificità massima */
#inertialMassModal .modal-body {
    position: relative !important;
    flex: 1 1 auto !important;
    padding: 1.5rem !important; /* Aumentato padding per più spazio */
    overflow-y: auto !important; /* Abilita lo scorrimento verticale se il contenuto è troppo lungo */
    background-color: #1E1E1E !important;
    color: #f8f9fa !important;
    max-height: calc(95vh - 180px) !important; /* Altezza aumentata per modal più grande */
}

/* Stile per il piè di pagina della modale - Specificità massima */
#inertialMassModal .modal-footer {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    justify-content: flex-end !important; /* Allinea i pulsanti a destra */
    padding: 0.5rem 1.5rem !important; /* Ridotto padding verticale */
    border-top: 1px solid #555e67 !important; /* Linea di separazione dall'area corpo */
    border-bottom-right-radius: calc(0.3rem - 1px) !important;
    border-bottom-left-radius: calc(0.3rem - 1px) !important;
    background-color: #1E1E1E !important;
}

#inertialMassModal .modal-footer > * {
    margin: 0.25rem !important;
}

#inertialMassModal .modal-footer button.btn-primary,
#inertialMassModal .modal-footer button.btn,
#inertialMassModal .modal-footer button {
    background-color: #D97706 !important; /* Arancione scuro per il pulsante primario */
    border-color: #C26A05 !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.25rem !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    cursor: pointer !important;
    border: 1px solid #C26A05 !important;
}

#inertialMassModal .modal-footer button.btn-primary:hover,
#inertialMassModal .modal-footer button.btn:hover,
#inertialMassModal .modal-footer button:hover {
    background-color: #C26A05 !important;
    border-color: #AD5E04 !important;
}

/* Sezioni form - Specificità massima */
#inertialMassModal .form-section {
    margin-bottom: 2rem !important;
    background-color: transparent !important;
    color: #f8f9fa !important;
}

#inertialMassModal .form-section h3 {
    color: #f8f9fa !important; /* Assicura che il titolo sia chiaro */
    margin-bottom: 1rem !important;
    font-size: 1.1rem !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    background-color: transparent !important;
}

#inertialMassModal .section-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 1rem !important;
    background-color: transparent !important;
}

/* Info grid per dati automatici - Specificità massima */
#inertialMassModal .info-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important; /* Aumentato minimo per più spazio */
    gap: 1.25rem !important; /* Aumentato gap */
    background-color: #2a2a2a !important; /* Sfondo scuro per i dati */
    padding: 1.5rem !important; /* Aumentato padding */
    border-radius: 10px !important; /* Angoli più arrotondati */
    border: 1px solid #555e67 !important; /* Bordo più coerente */
    margin-bottom: 1.5rem !important; /* Spazio sotto la griglia */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important; /* Ombra leggera */
}

#inertialMassModal .info-item {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.25rem !important;
    background-color: transparent !important;
}

#inertialMassModal .info-item label {
    font-size: 0.9rem !important; /* Leggermente più grande */
    color: #D97706 !important; /* Colore arancione per le etichette */
    font-family: 'Segoe UI', Arial, sans-serif !important;
    background-color: transparent !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

#inertialMassModal .info-item span {
    font-weight: 600 !important;
    color: #f8f9fa !important; /* Assicura che i dati siano chiari */
    font-family: 'Segoe UI', Arial, sans-serif !important;
    background-color: transparent !important;
    font-size: 1.1rem !important; /* Leggermente più grande */
    padding: 0.25rem 0 !important; /* Spazio verticale */
}

/* Form elements - Specificità massima */
#inertialMassModal .form-row {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
    margin-bottom: 1rem !important;
    background-color: transparent !important;
}

#inertialMassModal .form-group {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
    background-color: transparent !important;
}

#inertialMassModal .form-group label {
    font-size: 0.875rem !important;
    color: #f8f9fa !important; /* Assicura che le etichette siano chiare */
    font-weight: 500 !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    background-color: transparent !important;
}

#inertialMassModal #inertialMassForm label {
    color: #f8f9fa !important;
    font-weight: 500 !important; /* Leggermente più marcato per leggibilità */
    margin-bottom: 0.3rem !important; /* Spazio sotto l'etichetta */
    display: block !important; /* Assicura che occupi la sua riga */
    font-family: 'Segoe UI', Arial, sans-serif !important;
    background-color: transparent !important;
}

#inertialMassModal #inertialMassForm .form-control,
#inertialMassModal #inertialMassForm input[type="text"],
#inertialMassModal #inertialMassForm input[type="number"],
#inertialMassModal #inertialMassForm input[type="date"],
#inertialMassModal #inertialMassForm select {
    background-color: #404040 !important; /* Sfondo più scuro per i campi, per contrasto con il corpo modale, FORZATO */
    color: #f8f9fa !important; /* FORZATO */
    border: 1px solid #555e67 !important; /* FORZATO */
    padding: 0.65rem 0.75rem !important; /* Leggermente aggiustato il padding */
    border-radius: 0.25rem !important; /* Angoli meno arrotondati per un look più sharp */
    width: 100% !important; /* Assicura che i campi occupino tutta la larghezza disponibile nel loro contenitore */
    box-sizing: border-box !important; /* Include padding e border nella larghezza/altezza totale */
    font-family: 'Segoe UI', Arial, sans-serif !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
}

#inertialMassModal #inertialMassForm select:focus,
#inertialMassModal #inertialMassForm input:focus {
    background-color: #404040 !important; /* Sfondo leggermente più chiaro al focus */
    border-color: #80bdff !important;
    color: #f8f9fa !important;
    outline: none !important; /* Rimuove l'outline di default del browser */
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important; /* Ombra per indicare il focus */
}

/* Rimuoviamo la regola per le option, il browser usa lo stile nativo */

/* Piani container */
#floors-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.floor-item {
    background-color: #404040; /* Sfondo scuro per i piani */
    border: 1px solid #6c757d;
    border-radius: 8px;
    padding: 1rem;
    position: relative;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.floor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.floor-header h4 {
    margin: 0;
    color: #f8f9fa; /* Assicura che il titolo sia chiaro */
}

.btn-remove-floor {
    background: none;
    border: none;
    color: #dc3545; /* Colore rosso per il pulsante di rimozione */
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.btn-remove-floor:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

/* Bottoni */
.add-floor-btn {
    background-color: #6c757d; /* Grigio scuro come i pulsanti secondari */
    color: white;
    border: 1px solid #6c757d;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
    font-size: 0.9rem;
    margin-bottom: 1rem; /* Aggiunto spazio sotto */
}

.add-floor-btn:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* Pulsante Test Event Listener */
.test-event-btn {
    background-color: #D97706 !important;
    color: white !important;
    border: 1px solid #C26A05 !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.25rem !important;
    cursor: pointer !important;
    transition: all 0.15s ease-in-out !important;
    font-size: 0.875rem !important;
    margin-top: 0.5rem !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.test-event-btn:hover {
    background-color: #C26A05 !important;
    border-color: #AD5E04 !important;
}

.test-event-btn::before {
    content: "🔧" !important;
}

/* Stili per la tabella dei piani (se presente, per coerenza) */
.floor-table {
    border-radius: 0.25rem;
}

/* Stili per la barra di scorrimento (WebKit) */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #3E444A; /* Sfondo della traccia leggermente più chiaro dello sfondo del corpo */
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background-color: #6c757d; /* Colore del cursore della scrollbar (grigio) */
    border-radius: 4px;
    border: 2px solid #3E444A; /* Crea un piccolo padding attorno al thumb */
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background-color: #5a6268; /* Cursore più scuro all'hover */
}

/* Sezione Risultati */
.modal-results,
#results-section {
    margin-top: 2rem;
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
    display: none !important; /* Nascosta inizialmente */
}

/* Mostra i risultati quando la classe è attiva */
.modal-results.show,
#results-section.show {
    display: block !important;
}

/* Stili per la barra di scorrimento della sezione risultati */
.modal-results::-webkit-scrollbar {
    width: 8px;
}

.modal-results::-webkit-scrollbar-track {
    background: #3E444A;
    border-radius: 4px;
}

.modal-results::-webkit-scrollbar-thumb {
    background-color: #6c757d;
    border-radius: 4px;
    border: 2px solid #3E444A;
}

.modal-results::-webkit-scrollbar-thumb:hover {
    background-color: #5a6268;
}

/* Stili per i risultati professionali */
.results-summary {
    background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
    border: 1px solid #555e67;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.results-summary h4 {
    color: #D97706;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
    border-bottom: 2px solid #D97706;
    padding-bottom: 0.5rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background-color 0.2s ease;
}

.result-item:last-child {
    border-bottom: none;
}

.result-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding-left: 1rem;
    padding-right: 1rem;
}

.result-label {
    font-weight: 500;
    color: #f8f9fa;
    font-size: 1.1rem;
}

.result-value {
    font-weight: 700;
    color: #D97706;
    font-size: 1.2rem;
    background: rgba(217, 119, 6, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: 1px solid rgba(217, 119, 6, 0.3);
}

.results-details {
    background-color: #2a2a2a;
    border: 1px solid #555e67;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.results-details h4 {
    color: #f8f9fa;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-details h4::before {
    content: "📊";
    font-size: 1.2rem;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #1e1e1e;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.results-table thead {
    background: linear-gradient(135deg, #D97706 0%, #C26A05 100%);
}

.results-table th {
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: white;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.results-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.results-table tbody tr:hover {
    background-color: rgba(217, 119, 6, 0.1);
}

.results-table tbody tr:last-child td {
    border-bottom: none;
}

.results-analysis {
    background: linear-gradient(135deg, #1a4d3a 0%, #2d5a47 100%);
    border: 1px solid #4a7c59;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.results-analysis h4 {
    color: #4ade80;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-analysis h4::before {
    content: "🤖";
    font-size: 1.1rem;
}

.analysis-content {
    color: #e5f3e8;
    line-height: 1.5;
    font-size: 0.95rem;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* Scrollbar per l'analisi AI */
.analysis-content::-webkit-scrollbar {
    width: 6px;
}

.analysis-content::-webkit-scrollbar-track {
    background: rgba(74, 124, 89, 0.3);
    border-radius: 3px;
}

.analysis-content::-webkit-scrollbar-thumb {
    background-color: #4ade80;
    border-radius: 3px;
}

.analysis-content::-webkit-scrollbar-thumb:hover {
    background-color: #22c55e;
}

/* Stili per il messaggio di caricamento */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(30, 30, 30, 0.95);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0.3rem;
    backdrop-filter: blur(4px);
}

.loading-content {
    text-align: center;
    color: #f8f9fa;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(217, 119, 6, 0.3);
    border-top: 4px solid #D97706;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #D97706;
}

.loading-message {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.loading-progress {
    width: 300px;
    height: 4px;
    background-color: rgba(217, 119, 6, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 1rem;
}

.loading-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #D97706, #F59E0B);
    border-radius: 2px;
    animation: progress 3s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.loading-steps {
    margin-top: 1.5rem;
    text-align: left;
    max-width: 400px;
}

.loading-step {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
    font-size: 0.9rem;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.loading-step.active {
    opacity: 1;
    color: #D97706;
}

.loading-step.completed {
    opacity: 0.8;
    color: #4ade80;
}

.loading-step-icon {
    width: 16px;
    height: 16px;
    margin-right: 0.8rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.loading-step.active .loading-step-icon {
    background-color: #D97706;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-step.completed .loading-step-icon {
    background-color: #4ade80;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Miglioramenti per il pulsante di calcolo */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-loader {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.spinner {
    animation: spin 1s linear infinite;
}

.spinner .path {
    stroke: currentColor;
    stroke-linecap: round;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 5;
    animation: dash 1.5s ease-in-out infinite;
}

@keyframes dash {
    0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35;
    }
    100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124;
    }
}

/* Regole aggiuntive per forzare la visibilità degli elementi */
#inertialMassModal * {
    font-family: 'Segoe UI', Arial, sans-serif !important;
}

#inertialMassModal button {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: white !important;
    background-color: #D97706 !important;
    border: 1px solid #C26A05 !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.25rem !important;
    cursor: pointer !important;
    font-size: 0.9rem !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
}

#inertialMassModal input,
#inertialMassModal select,
#inertialMassModal textarea {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background-color: #404040 !important;
    color: #f8f9fa !important;
    border: 1px solid #555e67 !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
}

#inertialMassModal label {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #f8f9fa !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
}

/* Responsive */
@media (max-width: 768px) {
    #inertialMassModal .modal-container {
        width: 98% !important;        /* Mobile: quasi pieno schermo */
        max-height: 98vh !important;  /* Mobile: altezza massima aumentata */
    }

    #inertialMassModal .form-row {
        grid-template-columns: 1fr !important;
    }

    #inertialMassModal .info-grid {
        grid-template-columns: 1fr !important;
    }

    .results-summary,
    .results-details,
    .results-analysis {
        padding: 1.5rem;
    }

    .result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .results-table {
        font-size: 0.9rem;
    }

    .results-table th,
    .results-table td {
        padding: 0.75rem 0.5rem;
    }

    .loading-progress {
        width: 250px;
    }

    .loading-steps {
        max-width: 300px;
    }
}
