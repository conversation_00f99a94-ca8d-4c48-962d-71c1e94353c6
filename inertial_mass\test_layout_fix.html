<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖥️ Test Layout Fix - Modal Massa Inerziale</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CSS del Modal -->
    <link rel="stylesheet" href="assets/css/modal.css">
    
    <style>
        body {
            background-color: #121212;
            color: #f8f9fa;
            font-family: 'Segoe UI', Arial, sans-serif;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
        }
        
        .test-btn {
            background-color: #D97706;
            color: white;
            border: 1px solid #C26A05;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1.1rem;
            margin: 1rem;
            transition: all 0.2s ease;
        }
        
        .test-btn:hover {
            background-color: #C26A05;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3);
        }
        
        .test-btn.success {
            background-color: #28a745;
            border-color: #1e7e34;
        }
        
        .test-btn.success:hover {
            background-color: #1e7e34;
        }
        
        .test-btn.danger {
            background-color: #dc3545;
            border-color: #c82333;
        }
        
        .test-btn.danger:hover {
            background-color: #c82333;
        }
        
        .info-box {
            background-color: #2a2a2a;
            border: 1px solid #555e67;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: left;
        }
        
        .info-box h3 {
            color: #D97706;
            margin-bottom: 1rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .status-box {
            background-color: #1a1a1a;
            border: 2px solid #555e67;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🖥️ Test Correzione Layout e Pulsante Chiusura</h1>
        <p>Test per verificare che i risultati utilizzino tutto lo spazio del modal e che il pulsante X funzioni</p>
        
        <div class="info-box">
            <h3>🎯 Problemi da Risolvere:</h3>
            <ul>
                <li>✅ <strong>Layout Risultati:</strong> I risultati devono utilizzare tutta la finestra del modal</li>
                <li>✅ <strong>Pulsante Chiusura:</strong> La X in alto a destra deve funzionare</li>
                <li>✅ <strong>Transizioni:</strong> Animazioni fluide tra form e risultati</li>
                <li>✅ <strong>Reset:</strong> Il pulsante "Nuovo Calcolo" deve ripristinare il form</li>
            </ul>
        </div>
        
        <div class="test-grid">
            <button class="test-btn" onclick="openModal()">
                🚀 Apri Modal
            </button>
            
            <button class="test-btn success" onclick="testResults()">
                📊 Test Risultati Schermo Intero
            </button>
            
            <button class="test-btn danger" onclick="testCloseButton()">
                ❌ Test Pulsante Chiusura
            </button>
        </div>
        
        <div id="status-box" class="status-box">
            <strong>📊 Status Test:</strong><br>
            Pronto per iniziare i test...
        </div>
        
        <div class="info-box">
            <h3>📋 Sequenza di Test:</h3>
            <ol>
                <li><strong>Apri Modal</strong> - Verifica apertura corretta</li>
                <li><strong>Test Risultati Schermo Intero</strong> - Simula calcolo e verifica layout</li>
                <li><strong>Test Pulsante Chiusura</strong> - Verifica che la X funzioni</li>
                <li><strong>Test Manuale:</strong>
                    <ul>
                        <li>Prova a cliccare la X in alto a destra</li>
                        <li>Prova ESC sulla tastiera</li>
                        <li>Verifica che i risultati occupino tutto lo spazio</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript del Modal -->
    <script src="assets/js/modal.js"></script>
    
    <script>
        // Simula dati ASDP per il test
        window.asdpData = {
            coordinates: "42.3601° N, 13.3995° E",
            zone: "Zona 1",
            category: "Categoria A",
            ag: "0.261g",
            f0: "2.47",
            tc_star: "0.31s"
        };
        
        let statusBox = document.getElementById('status-box');
        
        function logStatus(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : '📝';
            
            statusBox.innerHTML += `<br>${icon} ${timestamp}: ${message}`;
            statusBox.scrollTop = statusBox.scrollHeight;
            
            console.log(`${icon} ${message}`);
        }
        
        function openModal() {
            logStatus('🔧 Apertura modal per test layout...');
            
            // Carica il modal
            fetch('modal.php')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modal-container').innerHTML = html;
                    
                    // Inizializza il modal
                    if (typeof initializeInertialMassModal === 'function') {
                        initializeInertialMassModal();
                    }
                    
                    // Mostra il modal
                    const modal = document.getElementById('inertialMassModal');
                    if (modal) {
                        modal.style.display = 'flex';
                        logStatus('Modal aperto con successo', 'success');
                        
                        // Popola i dati automatici
                        populateSeismicData();
                    } else {
                        logStatus('Modal non trovato nel DOM', 'error');
                    }
                })
                .catch(error => {
                    logStatus('Errore nel caricamento del modal: ' + error.message, 'error');
                });
        }
        
        function populateSeismicData() {
            // Popola i dati sismici automatici
            const elements = {
                'im-coordinates': window.asdpData.coordinates,
                'im-zone': window.asdpData.zone,
                'im-category': window.asdpData.category,
                'im-ag': window.asdpData.ag,
                'im-f0': window.asdpData.f0,
                'im-tc-star': window.asdpData.tc_star
            };
            
            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }
        
        function testResults() {
            logStatus('📊 Test risultati schermo intero...');
            
            const modal = document.getElementById('inertialMassModal');
            if (!modal || modal.style.display !== 'flex') {
                logStatus('Apri prima il modal!', 'error');
                return;
            }
            
            // Simula risultati di test con dati più complessi
            const mockResults = {
                total_mass: 1847.25,
                period: 0.892,
                total_force: 4821.67,
                floor_forces: [
                    { level: "Piano Terra", mass: 923.62, height: 3.5, force: 1607.22 },
                    { level: "Piano 1", mass: 923.63, height: 7.0, force: 3214.45 },
                    { level: "Copertura", mass: 0.0, height: 10.5, force: 0.0 }
                ],
                llm_analysis: "L'analisi strutturale indica una distribuzione equilibrata delle masse tra i piani. Il periodo fondamentale di 0.892s è appropriato per la tipologia strutturale in cemento armato. Le forze sismiche risultano ben distribuite, con il piano superiore che riceve la maggior parte del carico sismico come previsto dalla normativa. La struttura presenta caratteristiche dinamiche compatibili con i requisiti di sicurezza sismica."
            };
            
            // Chiama la funzione displayResults se disponibile
            if (typeof displayResults === 'function') {
                logStatus('Chiamando displayResults con dati mock complessi', 'success');
                displayResults(mockResults);
                
                // Verifica che i risultati occupino tutto lo spazio
                setTimeout(() => {
                    const resultsSection = document.getElementById('results-section');
                    if (resultsSection && resultsSection.classList.contains('show')) {
                        logStatus('Risultati mostrati correttamente a schermo intero', 'success');
                        
                        // Verifica dimensioni
                        const rect = resultsSection.getBoundingClientRect();
                        logStatus(`Dimensioni risultati: ${rect.width}x${rect.height}px`);
                    } else {
                        logStatus('Risultati non mostrati correttamente', 'error');
                    }
                }, 1000);
            } else {
                logStatus('Funzione displayResults non disponibile', 'error');
            }
        }
        
        function testCloseButton() {
            logStatus('❌ Test pulsante chiusura...');
            
            const modal = document.getElementById('inertialMassModal');
            if (!modal || modal.style.display !== 'flex') {
                logStatus('Apri prima il modal!', 'error');
                return;
            }
            
            // Trova il pulsante di chiusura
            const closeButton = document.querySelector('.modal-close');
            if (closeButton) {
                logStatus('Pulsante chiusura trovato, simulando click...');
                
                // Simula click
                closeButton.click();
                
                // Verifica che il modal si chiuda
                setTimeout(() => {
                    if (modal.style.display === 'none') {
                        logStatus('Pulsante chiusura funziona correttamente!', 'success');
                    } else {
                        logStatus('Pulsante chiusura NON funziona', 'error');
                    }
                }, 500);
            } else {
                logStatus('Pulsante chiusura non trovato', 'error');
            }
        }
        
        // Auto-inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            logStatus('🖥️ Test Layout Fix caricato');
        });
    </script>
</body>
</html>
