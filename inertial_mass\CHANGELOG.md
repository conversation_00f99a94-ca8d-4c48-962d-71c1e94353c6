# Changelog - <PERSON><PERSON><PERSON> Massa Inerziale

Tutte le modifiche significative al modulo massa inerziale saranno documentate in questo file.

Il formato è basato su [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
e questo progetto aderisce al [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.4.2] - 2025-06-15

### ✨ Aggiunte
- **Modal Ingrandito**: Dimensioni aumentate a 1400px larghezza massima
- **Risultati Schermo Intero**: Visualizzazione ottimizzata senza form visibile
- **UX Migliorata**: Navigazione semplificata con pulsante "Nuovo Calcolo"

### 🔧 Modifiche
- **CSS Forzato**: Specificità massima per evitare conflitti con altri CSS
- **Viewport Units**: Uso di vw/vh per dimensioni assolute del modal
- **Layout Risultati**: Posizionamento assoluto per occupare tutto lo spazio

### 🗑️ Rimozioni
- **Pulsante Test Debug**: Rimosso elemento di test non più necessario
- **CSS Debug**: Puliti stili per elementi di debug

### 🐛 Correzioni
- **Cache Browser**: Aggiunto timestamp per forzare refresh CSS
- **Centratura Modal**: Riattivato display flex per centratura corretta

## [2.4.1] - 2025-06-15

### 🐛 Correzioni Critiche
- **Pulsante Chiusura**: Risolto problema pulsante X non funzionante
- **CSS Conflitti**: Rimosso display flex forzato che impediva chiusura
- **Event Listener**: Ottimizzata gestione eventi per chiusura modal

### 🧹 Pulizia Codebase
- **File Debug**: Rimossi tutti i file di test e debug
- **Documentazione**: Puliti report temporanei di troubleshooting

## [2.1.0] - 2025-01-04

### 🐛 Correzioni
- **CRITICO**: Risolto problema raddoppio icone nei titoli delle sezioni risultati
  - Le icone 📊 Distribuzione Forze per Piano e 🤖 Analisi AI apparivano duplicate
  - Causa: Mancanza di pulizia del contenuto HTML esistente prima dell'inserimento di nuovi risultati
  - Soluzione: Aggiunta di `resultsContent.innerHTML = '';` nella funzione `displayResults()`
  - File modificato: `assets/js/modal.js` (righe 858, 907-962)

### ✨ Miglioramenti
- **UI**: Ottimizzate icone della tabella per maggiore chiarezza
  - 🏢 PIANO → 🏢 PIANO
  - ⚖️ MASSA (T) → ⚖️ MASSA (T)  
  - 📏 ALTEZZA (M) → 📏 ALTEZZA (M)
  - 💥 FORZA (KN) → ⭐ FORZA (KN)
- **Performance**: Ridotto tempo di rendering dei risultati
- **Debug**: Aggiunto logging dettagliato per troubleshooting
- **Memory**: Prevenzione memory leak da duplicazioni HTML

### 🔧 Tecnico
- Migliorata gestione del DOM dinamico
- Ottimizzato sistema di pulizia HTML
- Stabilizzato il rendering delle animazioni
- Confermato funzionamento scroll verticale

### 📚 Documentazione
- Aggiornato README.md con dettagli bug fix
- Aggiornata documentazione tecnica (docs/14_massa_inerziale.md)
- Aggiornato troubleshooting (docs/07_troubleshooting.md)
- Creato questo CHANGELOG.md

### ✅ Test
- Test raddoppio icone: ✅ Risoluzione confermata
- Test scroll verticale: ✅ Funzionamento verificato  
- Test performance: ✅ Rendering ottimizzato
- Test cross-browser: ✅ Compatibilità mantenuta

## [2.0.0] - 2024-12-01

### ✨ Nuove Funzionalità
- **Modulo completo**: Prima release del modulo massa inerziale
- **Integrazione AI**: Connessione con Deepseek LLM per calcoli avanzati
- **Interfaccia modale**: UI responsive con scroll verticale
- **Calcolo automatico**: Massa inerziale sismica secondo NTC 2018
- **Cache intelligente**: Sistema di ottimizzazione performance
- **Rate limiting**: Protezione API (10 richieste/minuto)

### 🗄️ Database
- Creazione 4 tabelle principali:
  - `inertial_mass_calculations` - Calcoli principali
  - `inertial_mass_floor_details` - Dettagli per piano
  - `inertial_mass_cache` - Cache per performance
  - `inertial_mass_api_logs` - Log chiamate API

### 🔌 API
- **LLM Service** (`api/llm_service.php`): Integrazione Deepseek
- **Data Service** (`api/data_service.php`): Recupero dati sismici
- **Save Results** (`api/save_results.php`): Salvataggio nel database

### 🎨 Frontend
- **Modal CSS** (`assets/css/modal.css`): Stili responsive
- **Modal JS** (`assets/js/modal.js`): Logica interfaccia
- **Animazioni**: Transizioni fluide e feedback visivo
- **Validazione**: Controlli input lato client

### 🔒 Sicurezza
- Autenticazione tramite sessioni ASDP
- Validazione e sanitizzazione input
- Rate limiting per protezione API
- Logging completo per audit

### 🏗️ Integrazione ASDP
- Pulsante dedicato nella sezione parametri sismici
- Recupero automatico dati sismici esistenti
- Salvataggio risultati nel database principale
- Compatibilità con sistema di autenticazione

## [1.0.0] - 2024-11-01

### 🎯 Pianificazione
- Definizione architettura modulo
- Analisi requisiti tecnici
- Studio integrazione con ASDP
- Progettazione database

---

## Legenda

- 🐛 **Correzioni**: Bug fix e risoluzione problemi
- ✨ **Nuove Funzionalità**: Aggiunte di nuove caratteristiche
- 🔧 **Tecnico**: Miglioramenti tecnici e refactoring
- 📚 **Documentazione**: Aggiornamenti documentazione
- ✅ **Test**: Validazioni e test
- 🗄️ **Database**: Modifiche struttura dati
- 🔌 **API**: Endpoint e servizi
- 🎨 **Frontend**: Interfaccia utente
- 🔒 **Sicurezza**: Aspetti di sicurezza
- 🏗️ **Integrazione**: Connessioni con altri sistemi
- 🎯 **Pianificazione**: Attività di progettazione

## Link Utili

- [README Modulo](README.md)
- [Documentazione Tecnica](../docs/14_massa_inerziale.md)
- [Troubleshooting](../docs/07_troubleshooting.md)
- [README Principale](../README.md)