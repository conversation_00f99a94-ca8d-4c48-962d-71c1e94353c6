/**
 * debug_modal.js - Script di debug per il modal massa inerziale
 * Path: /inertial_mass/debug_modal.js
 */

// Funzione di debug per analizzare la visibilità del modal
function debugModalVisibility() {
    console.log('=== DEBUG MODAL MASSA INERZIALE ===');
    
    const modal = document.getElementById('inertialMassModal');
    if (!modal) {
        console.error('❌ Modal non trovato nel DOM!');
        return;
    }
    
    console.log('✅ Modal trovato nel DOM');
    
    // Analizza gli stili del modal
    const modalStyles = window.getComputedStyle(modal);
    console.log('📊 Stili Modal:', {
        display: modalStyles.display,
        visibility: modalStyles.visibility,
        opacity: modalStyles.opacity,
        position: modalStyles.position,
        zIndex: modalStyles.zIndex,
        backgroundColor: modalStyles.backgroundColor,
        color: modalStyles.color,
        width: modalStyles.width,
        height: modalStyles.height
    });
    
    // Analizza il container
    const container = modal.querySelector('.modal-container');
    if (container) {
        const containerStyles = window.getComputedStyle(container);
        console.log('📊 Stili Container:', {
            display: containerStyles.display,
            visibility: containerStyles.visibility,
            opacity: containerStyles.opacity,
            backgroundColor: containerStyles.backgroundColor,
            color: containerStyles.color,
            width: containerStyles.width,
            height: containerStyles.height
        });
    }
    
    // Analizza il body del modal
    const body = modal.querySelector('.modal-body');
    if (body) {
        const bodyStyles = window.getComputedStyle(body);
        console.log('📊 Stili Body:', {
            display: bodyStyles.display,
            visibility: bodyStyles.visibility,
            opacity: bodyStyles.opacity,
            backgroundColor: bodyStyles.backgroundColor,
            color: bodyStyles.color
        });
    }
    
    // Analizza i form elements
    const formElements = modal.querySelectorAll('input, select, label, button');
    console.log(`🔍 Trovati ${formElements.length} elementi form`);
    
    formElements.forEach((element, index) => {
        const styles = window.getComputedStyle(element);
        const isVisible = styles.display !== 'none' && 
                         styles.visibility !== 'hidden' && 
                         parseFloat(styles.opacity) > 0;
        
        console.log(`Element ${index + 1} (${element.tagName}):`, {
            visible: isVisible,
            display: styles.display,
            visibility: styles.visibility,
            opacity: styles.opacity,
            backgroundColor: styles.backgroundColor,
            color: styles.color
        });
    });
    
    // Verifica CSS caricati
    console.log('📄 CSS caricati:');
    for (let i = 0; i < document.styleSheets.length; i++) {
        const sheet = document.styleSheets[i];
        if (sheet.href) {
            console.log(`  - ${sheet.href}`);
            if (sheet.href.includes('modal.css')) {
                console.log('    ✅ CSS Modal trovato!');
            }
        }
    }
}

// Funzione per forzare la visibilità degli elementi
function forceModalVisibility() {
    console.log('🔧 Forzando visibilità modal...');
    
    const modal = document.getElementById('inertialMassModal');
    if (!modal) {
        console.error('❌ Modal non trovato!');
        return;
    }
    
    // Forza stili inline per il modal
    modal.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(0, 0, 0, 0.7) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 99999 !important;
        visibility: visible !important;
        opacity: 1 !important;
    `;
    
    // Forza stili per il container
    const container = modal.querySelector('.modal-container');
    if (container) {
        container.style.cssText = `
            background-color: #1E1E1E !important;
            color: #f8f9fa !important;
            border: 1px solid #555e67 !important;
            border-radius: 0.3rem !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
            display: flex !important;
            flex-direction: column !important;
            width: 90% !important;
            max-width: 800px !important;
            max-height: 85vh !important;
            visibility: visible !important;
            opacity: 1 !important;
        `;
    }
    
    // Forza stili per il body
    const body = modal.querySelector('.modal-body');
    if (body) {
        body.style.cssText = `
            background-color: #1E1E1E !important;
            color: #f8f9fa !important;
            padding: 1rem 1.5rem !important;
            overflow-y: auto !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        `;
    }
    
    // Forza stili per tutti gli elementi form
    const formElements = modal.querySelectorAll('input, select, label, button, .form-section, .form-group, .info-grid, .info-item');
    formElements.forEach(element => {
        element.style.visibility = 'visible';
        element.style.opacity = '1';
        element.style.display = element.tagName === 'INPUT' || element.tagName === 'SELECT' ? 'block' : '';
        element.style.color = '#f8f9fa';
        element.style.fontFamily = 'Segoe UI, Arial, sans-serif';
        
        if (element.tagName === 'INPUT' || element.tagName === 'SELECT') {
            element.style.backgroundColor = '#404040';
            element.style.border = '1px solid #555e67';
            element.style.padding = '0.65rem 0.75rem';
            element.style.borderRadius = '0.25rem';
            element.style.width = '100%';
        }
        
        if (element.tagName === 'BUTTON') {
            element.style.backgroundColor = '#D97706';
            element.style.color = 'white';
            element.style.border = '1px solid #C26A05';
            element.style.padding = '0.5rem 1rem';
            element.style.borderRadius = '0.25rem';
            element.style.cursor = 'pointer';
        }
    });
    
    console.log('✅ Visibilità forzata applicata');
}

// Funzione per testare l'apertura del modal
function testModalOpen() {
    console.log('🧪 Test apertura modal...');
    
    const modal = document.getElementById('inertialMassModal');
    if (modal) {
        modal.style.display = 'flex';
        
        setTimeout(() => {
            debugModalVisibility();
            
            // Se gli elementi non sono visibili, forza la visibilità
            const firstInput = modal.querySelector('input');
            if (firstInput) {
                const styles = window.getComputedStyle(firstInput);
                if (styles.display === 'none' || styles.visibility === 'hidden' || parseFloat(styles.opacity) === 0) {
                    console.log('⚠️ Elementi non visibili, forzando visibilità...');
                    forceModalVisibility();
                }
            }
        }, 100);
    }
}

// Funzione per chiudere il modal
function testModalClose() {
    console.log('🔒 Chiusura modal test...');
    const modal = document.getElementById('inertialMassModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Aggiungi funzioni al window per uso globale
window.debugModalVisibility = debugModalVisibility;
window.forceModalVisibility = forceModalVisibility;
window.testModalOpen = testModalOpen;
window.testModalClose = testModalClose;

// Auto-debug quando il modal viene aperto
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Debug modal script caricato');
    
    // Osserva i cambiamenti al modal
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                const modal = mutation.target;
                if (modal.id === 'inertialMassModal' && modal.style.display === 'flex') {
                    console.log('👁️ Modal aperto, eseguendo debug...');
                    setTimeout(debugModalVisibility, 200);
                }
            }
        });
    });
    
    // Inizia l'osservazione quando il modal è presente
    const checkModal = setInterval(() => {
        const modal = document.getElementById('inertialMassModal');
        if (modal) {
            observer.observe(modal, { attributes: true, attributeFilter: ['style'] });
            clearInterval(checkModal);
            console.log('👀 Observer attivato per il modal');
        }
    }, 1000);
});

console.log('📝 Debug script massa inerziale caricato');
