<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Test Rollback - Modal Massa Inerziale</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CSS del Modal -->
    <link rel="stylesheet" href="assets/css/modal.css">
    
    <style>
        body {
            background-color: #121212;
            color: #f8f9fa;
            font-family: 'Segoe UI', Arial, sans-serif;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .test-btn {
            background-color: #28a745;
            color: white;
            border: 1px solid #1e7e34;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1.1rem;
            margin: 1rem;
            transition: all 0.2s ease;
        }
        
        .test-btn:hover {
            background-color: #1e7e34;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .info-box {
            background-color: #2a2a2a;
            border: 1px solid #555e67;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: left;
        }
        
        .info-box h3 {
            color: #28a745;
            margin-bottom: 1rem;
        }
        
        .status-box {
            background-color: #1a1a1a;
            border: 2px solid #555e67;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 Test Rollback Conservativo</h1>
        <p>Verifica che il funzionamento base sia stato ripristinato</p>
        
        <div class="info-box">
            <h3>✅ Rollback Completato:</h3>
            <ul>
                <li>🔧 <strong>Rimossa funzione initCloseButtonListener</strong> - Eliminati conflitti</li>
                <li>❌ <strong>Ripristinato pulsante chiusura onclick</strong> - Funzionamento originale</li>
                <li>🖥️ <strong>Ripristinato CSS layout risultati</strong> - Layout normale</li>
                <li>🔄 <strong>Semplificate funzioni displayResults e resetCalculation</strong> - Meno complessità</li>
                <li>📏 <strong>Ripristinati padding originali</strong> - Spaziatura corretta</li>
            </ul>
        </div>
        
        <button class="test-btn" onclick="testBasicFunctionality()">
            🧪 Test Funzionalità Base
        </button>
        
        <div id="status-box" class="status-box">
            <strong>📊 Status Test:</strong><br>
            Pronto per test rollback...
        </div>
        
        <div class="info-box">
            <h3>🎯 Test da Eseguire:</h3>
            <ol>
                <li><strong>Apertura Modal</strong> - Deve aprirsi correttamente</li>
                <li><strong>Pulsante X</strong> - Deve chiudere il modal</li>
                <li><strong>Event Listeners</strong> - Dropdown devono funzionare</li>
                <li><strong>Form Base</strong> - Validazione deve funzionare</li>
            </ol>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript del Modal -->
    <script src="assets/js/modal.js"></script>
    
    <script>
        // Simula dati ASDP per il test
        window.asdpData = {
            coordinates: "42.3601° N, 13.3995° E",
            seismic_zone: "Zona 1",
            soil_category: "Categoria A",
            ag: "0.261g",
            f0: "2.47",
            tc_star: "0.31s",
            damping: "5%",
            q0_factor: "3.0"
        };
        
        let statusBox = document.getElementById('status-box');
        
        function logStatus(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : '📝';
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            
            statusBox.innerHTML += `<br><span class="${className}">${icon} ${timestamp}: ${message}</span>`;
            statusBox.scrollTop = statusBox.scrollHeight;
            
            console.log(`${icon} ${message}`);
        }
        
        function testBasicFunctionality() {
            logStatus('🔄 Iniziando test funzionalità base...');

            // Test 1: Caricamento Modal
            logStatus('📥 Caricamento modal...');
            fetch('modal.php')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modal-container').innerHTML = html;
                    logStatus('Modal HTML caricato', 'success');

                    // Test 2: Inizializzazione
                    if (typeof initInertialMassModal === 'function') {
                        logStatus('Funzione initInertialMassModal trovata', 'success');

                        // Test 3: Apertura Modal
                        try {
                            initInertialMassModal(window.asdpData);
                            logStatus('Modal inizializzato con successo', 'success');

                            // Test 4: Verifica Pulsante Chiusura
                            setTimeout(() => {
                                testCloseButton();
                            }, 1000);

                        } catch (error) {
                            logStatus('Errore inizializzazione: ' + error.message, 'error');
                        }
                    } else {
                        logStatus('Funzione initInertialMassModal NON trovata', 'error');
                    }
                })
                .catch(error => {
                    logStatus('Errore caricamento: ' + error.message, 'error');
                });
        }

        function testCloseButton() {
            logStatus('🔍 Test dettagliato pulsante chiusura...');

            const closeButton = document.querySelector('.modal-close');
            if (closeButton) {
                logStatus('✅ Pulsante chiusura trovato', 'success');

                // Test onclick attribute
                const onclickAttr = closeButton.getAttribute('onclick');
                if (onclickAttr) {
                    logStatus(`✅ Onclick presente: ${onclickAttr}`, 'success');
                } else {
                    logStatus('❌ Onclick NON presente', 'error');
                }

                // Test funzione globale
                if (typeof window.closeInertialMassModal === 'function') {
                    logStatus('✅ Funzione window.closeInertialMassModal disponibile', 'success');
                } else {
                    logStatus('❌ Funzione window.closeInertialMassModal NON disponibile', 'error');
                }

                // Test funzione locale
                if (typeof closeInertialMassModal === 'function') {
                    logStatus('✅ Funzione closeInertialMassModal disponibile', 'success');
                } else {
                    logStatus('❌ Funzione closeInertialMassModal NON disponibile', 'error');
                }

                // Test click programmatico
                logStatus('🧪 Test click programmatico...');
                try {
                    closeButton.click();

                    // Verifica se il modal si è chiuso
                    setTimeout(() => {
                        const modal = document.getElementById('inertialMassModal');
                        if (modal && modal.style.display === 'none') {
                            logStatus('✅ Click programmatico FUNZIONA!', 'success');
                        } else {
                            logStatus('❌ Click programmatico NON funziona', 'error');

                            // Test manuale
                            logStatus('🎯 PROVA MANUALMENTE:', 'warning');
                            logStatus('1. Clicca la X in alto a destra', 'warning');
                            logStatus('2. Osserva la console per debug', 'warning');
                        }
                    }, 500);

                } catch (error) {
                    logStatus('❌ Errore click programmatico: ' + error.message, 'error');
                }

            } else {
                logStatus('❌ Pulsante chiusura NON trovato', 'error');
            }
        }
        
        // Auto-inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            logStatus('🔄 Test Rollback caricato - Pronto per test');
        });
    </script>
</body>
</html>
