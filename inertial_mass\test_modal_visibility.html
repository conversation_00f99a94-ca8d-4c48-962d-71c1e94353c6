<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Visibilità Modal Massa Inerziale</title>
    
    <!-- CSS del progetto principale per simulare i conflitti -->
    <link rel="stylesheet" href="../css/home.css">
    <link rel="stylesheet" href="../css/compact-info.css">
    
    <!-- CSS del modal -->
    <link rel="stylesheet" href="assets/css/modal.css">
    
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #2a2a2a;
            border-radius: 8px;
        }
        
        .test-button {
            background-color: #D97706;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .test-button:hover {
            background-color: #C26A05;
        }
        
        .debug-info {
            background-color: #333;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test Visibilità Modal Massa Inerziale</h1>
        
        <div class="debug-info">
            <h3>Debug Info:</h3>
            <p>Questo test verifica che il modal sia visibile anche con i CSS del progetto principale caricati.</p>
            <p>CSS caricati: home.css, compact-info.css, modal.css</p>
        </div>
        
        <button class="test-button" onclick="openTestModal()">
            🧪 Apri Modal Test
        </button>
        
        <button class="test-button" onclick="runVisibilityTest()">
            🔍 Test Visibilità Elementi
        </button>
        
        <button class="test-button" onclick="inspectStyles()">
            🎨 Ispeziona Stili
        </button>
        
        <div id="test-results" class="debug-info" style="display: none;">
            <h3>Risultati Test:</h3>
            <div id="test-output"></div>
        </div>
    </div>

    <!-- Modal HTML -->
    <div id="inertialMassModal" class="modal-overlay" style="display: none;">
        <div class="modal-container">
            <div class="modal-header">
                <h2>Calcolo Massa Inerziale Sismica</h2>
                <button type="button" class="modal-close" onclick="closeTestModal()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <div class="modal-body">
                <form id="inertialMassForm">
                    <!-- Sezione dati automatici da ASDP -->
                    <div class="form-section">
                        <h3>Dati Sismici (da ASDP)</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Coordinate:</label>
                                <span id="im-coordinates">41.9028, 12.4964</span>
                            </div>
                            <div class="info-item">
                                <label>Zona Sismica:</label>
                                <span id="im-seismic-zone">3</span>
                            </div>
                            <div class="info-item">
                                <label>ag:</label>
                                <span id="im-ag">0.062</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sezione dati da richiedere all'utente -->
                    <div class="form-section">
                        <h3>Caratteristiche Strutturali</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="im-construction-category">Tipologia Costruttiva *</label>
                                <select id="im-construction-category" name="construction_category" required>
                                    <option value="">Seleziona tipologia...</option>
                                    <option value="bridge">Ponte/Viadotto</option>
                                    <option value="building">Edificio</option>
                                    <option value="prefab_building">Edificio Prefabbricato</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="im-construction-year">Anno di Costruzione *</label>
                                <input type="number" id="im-construction-year" name="construction_year"
                                       min="1900" max="2025" required
                                       placeholder="es. 2010">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pulsanti azione -->
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="closeTestModal()">
                            Annulla
                        </button>
                        <button type="submit" class="btn btn-primary">
                            Calcola con AI
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openTestModal() {
            console.log('Apertura modal test...');
            const modal = document.getElementById('inertialMassModal');
            if (modal) {
                modal.style.display = 'flex';
                console.log('Modal aperto');
                
                // Test immediato della visibilità
                setTimeout(() => {
                    runVisibilityTest();
                }, 500);
            } else {
                console.error('Modal non trovato!');
            }
        }
        
        function closeTestModal() {
            console.log('Chiusura modal test...');
            const modal = document.getElementById('inertialMassModal');
            if (modal) {
                modal.style.display = 'none';
                console.log('Modal chiuso');
            }
        }
        
        function runVisibilityTest() {
            console.log('Esecuzione test visibilità...');
            const results = [];
            
            // Test elementi principali
            const elementsToTest = [
                '#inertialMassModal',
                '#inertialMassModal .modal-container',
                '#inertialMassModal .modal-header',
                '#inertialMassModal .modal-body',
                '#inertialMassModal .form-section',
                '#inertialMassModal .info-grid',
                '#inertialMassModal .form-group',
                '#inertialMassModal label',
                '#inertialMassModal input',
                '#inertialMassModal select',
                '#inertialMassModal button'
            ];
            
            elementsToTest.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    const styles = window.getComputedStyle(element);
                    const isVisible = styles.display !== 'none' && 
                                    styles.visibility !== 'hidden' && 
                                    styles.opacity !== '0';
                    
                    results.push({
                        selector: selector,
                        found: true,
                        visible: isVisible,
                        display: styles.display,
                        visibility: styles.visibility,
                        opacity: styles.opacity,
                        backgroundColor: styles.backgroundColor,
                        color: styles.color
                    });
                } else {
                    results.push({
                        selector: selector,
                        found: false,
                        visible: false
                    });
                }
            });
            
            displayTestResults(results);
        }
        
        function displayTestResults(results) {
            const resultsDiv = document.getElementById('test-results');
            const outputDiv = document.getElementById('test-output');
            
            let html = '<table style="width: 100%; border-collapse: collapse;">';
            html += '<tr style="background-color: #444;"><th>Elemento</th><th>Trovato</th><th>Visibile</th><th>Display</th><th>Visibility</th><th>Opacity</th><th>BG Color</th><th>Color</th></tr>';
            
            results.forEach(result => {
                const bgColor = result.visible ? '#2a5a2a' : '#5a2a2a';
                html += `<tr style="background-color: ${bgColor};">`;
                html += `<td style="padding: 5px; border: 1px solid #666;">${result.selector}</td>`;
                html += `<td style="padding: 5px; border: 1px solid #666;">${result.found ? '✅' : '❌'}</td>`;
                html += `<td style="padding: 5px; border: 1px solid #666;">${result.visible ? '✅' : '❌'}</td>`;
                html += `<td style="padding: 5px; border: 1px solid #666;">${result.display || 'N/A'}</td>`;
                html += `<td style="padding: 5px; border: 1px solid #666;">${result.visibility || 'N/A'}</td>`;
                html += `<td style="padding: 5px; border: 1px solid #666;">${result.opacity || 'N/A'}</td>`;
                html += `<td style="padding: 5px; border: 1px solid #666; font-size: 10px;">${result.backgroundColor || 'N/A'}</td>`;
                html += `<td style="padding: 5px; border: 1px solid #666; font-size: 10px;">${result.color || 'N/A'}</td>`;
                html += '</tr>';
            });
            
            html += '</table>';
            outputDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
        }
        
        function inspectStyles() {
            console.log('Ispezione stili CSS...');
            const modal = document.querySelector('#inertialMassModal');
            if (modal) {
                const styles = window.getComputedStyle(modal);
                console.log('Stili modal:', {
                    display: styles.display,
                    position: styles.position,
                    zIndex: styles.zIndex,
                    backgroundColor: styles.backgroundColor,
                    color: styles.color
                });
                
                const container = modal.querySelector('.modal-container');
                if (container) {
                    const containerStyles = window.getComputedStyle(container);
                    console.log('Stili container:', {
                        display: containerStyles.display,
                        backgroundColor: containerStyles.backgroundColor,
                        color: containerStyles.color
                    });
                }
            }
        }
        
        // Test automatico al caricamento
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Pagina caricata, CSS disponibili:', document.styleSheets.length);
            
            // Verifica che il CSS del modal sia caricato
            let modalCssLoaded = false;
            for (let i = 0; i < document.styleSheets.length; i++) {
                const sheet = document.styleSheets[i];
                if (sheet.href && sheet.href.includes('modal.css')) {
                    modalCssLoaded = true;
                    console.log('CSS modal caricato:', sheet.href);
                    break;
                }
            }
            
            if (!modalCssLoaded) {
                console.warn('CSS modal non trovato!');
            }
        });
    </script>
</body>
</html>
