<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CSS Fix - Modal Massa Inerziale</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CSS del Modal -->
    <link rel="stylesheet" href="assets/css/modal.css">
    
    <style>
        body {
            background-color: #121212;
            color: #f8f9fa;
            font-family: 'Segoe UI', Arial, sans-serif;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .test-btn {
            background-color: #D97706;
            color: white;
            border: 1px solid #C26A05;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1.1rem;
            margin: 1rem;
            transition: all 0.2s ease;
        }
        
        .test-btn:hover {
            background-color: #C26A05;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3);
        }
        
        .info-box {
            background-color: #2a2a2a;
            border: 1px solid #555e67;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: left;
        }
        
        .info-box h3 {
            color: #D97706;
            margin-bottom: 1rem;
        }
        
        .info-box ul {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Correzioni CSS Modal</h1>
        <p>Test per verificare le correzioni apportate al CSS del modal massa inerziale</p>
        
        <div class="info-box">
            <h3>📋 Correzioni Applicate:</h3>
            <ul>
                <li>✅ Header con sfondo differenziato e padding aumentato</li>
                <li>✅ Titolo più grande e leggibile</li>
                <li>✅ Pulsante chiusura migliorato con hover effect</li>
                <li>✅ Body con padding aumentato e altezza limitata</li>
                <li>✅ Info-grid con spacing migliorato e ombra</li>
                <li>✅ Etichette dati sismici in arancione</li>
                <li>✅ Sezione risultati nascosta inizialmente</li>
                <li>✅ Stili per pulsante test event listener</li>
            </ul>
        </div>
        
        <button class="test-btn" onclick="openModal()">
            🚀 Apri Modal Massa Inerziale
        </button>

        <button class="test-btn" onclick="testCalculation()" style="background-color: #28a745; border-color: #1e7e34;">
            🧮 Test Calcolo Risultati
        </button>
        
        <div class="info-box">
            <h3>🎯 Cosa Verificare:</h3>
            <ul>
                <li>Header con sfondo grigio più scuro</li>
                <li>Titolo ben visibile e dimensionato</li>
                <li>Sezione "Dati Sismici" ben formattata</li>
                <li>Spacing corretto tra le sezioni</li>
                <li>Campi input ben stilizzati</li>
                <li>Sezione risultati non visibile inizialmente</li>
            </ul>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript del Modal -->
    <script src="assets/js/modal.js"></script>
    
    <script>
        // Simula dati ASDP per il test
        window.asdpData = {
            coordinates: "42.3601° N, 13.3995° E",
            zone: "Zona 1",
            category: "Categoria A",
            ag: "0.261g",
            f0: "2.47",
            tc_star: "0.31s"
        };
        
        function openModal() {
            console.log('🔧 Apertura modal per test CSS...');
            
            // Carica il modal
            fetch('modal.php')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modal-container').innerHTML = html;
                    
                    // Inizializza il modal
                    if (typeof initializeInertialMassModal === 'function') {
                        initializeInertialMassModal();
                    }
                    
                    // Mostra il modal
                    const modal = document.getElementById('inertialMassModal');
                    if (modal) {
                        modal.style.display = 'flex';
                        console.log('✅ Modal aperto con successo');
                        
                        // Popola i dati automatici
                        populateSeismicData();
                    } else {
                        console.error('❌ Modal non trovato');
                    }
                })
                .catch(error => {
                    console.error('❌ Errore nel caricamento del modal:', error);
                });
        }
        
        function populateSeismicData() {
            // Popola i dati sismici automatici
            const elements = {
                'im-coordinates': window.asdpData.coordinates,
                'im-zone': window.asdpData.zone,
                'im-category': window.asdpData.category,
                'im-ag': window.asdpData.ag,
                'im-f0': window.asdpData.f0,
                'im-tc-star': window.asdpData.tc_star
            };

            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                    console.log(`✅ Popolato ${id}: ${value}`);
                }
            });
        }

        function testCalculation() {
            console.log('🧮 Test calcolo risultati...');

            // Simula risultati di test
            const mockResults = {
                total_mass: 1250.75,
                base_shear: 3251.95,
                fundamental_period: 0.85,
                floor_forces: [
                    { level: "Piano 1", mass: 625.38, height: 3.5, force: 1625.98 },
                    { level: "Piano 2", mass: 625.37, height: 7.0, force: 1625.97 }
                ],
                llm_analysis: "L'analisi strutturale indica una distribuzione equilibrata delle masse. Il periodo fondamentale di 0.85s è appropriato per la tipologia strutturale. Le forze sismiche risultano ben distribuite tra i piani."
            };

            // Verifica che il modal sia aperto
            const modal = document.getElementById('inertialMassModal');
            if (!modal || modal.style.display !== 'flex') {
                alert('⚠️ Apri prima il modal!');
                return;
            }

            // Chiama la funzione displayResults se disponibile
            if (typeof displayResults === 'function') {
                console.log('✅ Chiamando displayResults con dati mock');
                displayResults(mockResults);
            } else {
                console.error('❌ Funzione displayResults non trovata');
                alert('❌ Funzione displayResults non disponibile');
            }
        }
        
        // Test automatico all'avvio
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Test CSS Fix caricato');
            console.log('📊 Dati ASDP simulati:', window.asdpData);
        });
    </script>
</body>
</html>
