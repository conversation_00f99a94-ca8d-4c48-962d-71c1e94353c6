<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Correzione Pulsante Chiusura</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CSS del Modal -->
    <link rel="stylesheet" href="assets/css/modal.css">
    
    <style>
        body {
            background-color: #121212;
            color: #f8f9fa;
            font-family: 'Segoe UI', Arial, sans-serif;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .test-btn {
            background-color: #28a745;
            color: white;
            border: 1px solid #1e7e34;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1.1rem;
            margin: 1rem;
            transition: all 0.2s ease;
        }
        
        .test-btn:hover {
            background-color: #1e7e34;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .info-box {
            background-color: #2a2a2a;
            border: 1px solid #555e67;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: left;
        }
        
        .info-box h3 {
            color: #28a745;
            margin-bottom: 1rem;
        }
        
        .status-box {
            background-color: #1a1a1a;
            border: 2px solid #555e67;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Correzione Pulsante Chiusura</h1>
        <p>Verifica che il pulsante X ora funzioni correttamente</p>
        
        <div class="info-box">
            <h3>🔧 Correzione Applicata:</h3>
            <ul>
                <li>✅ <strong>Rimozione stili CSS forzati</strong> - modalElement.removeAttribute('style')</li>
                <li>✅ <strong>Impostazione display none</strong> - Dopo rimozione stili</li>
                <li>✅ <strong>Fallback aggiornato</strong> - Anche event listener usa la stessa logica</li>
                <li>✅ <strong>ESC key supportato</strong> - Funziona anche con tastiera</li>
            </ul>
        </div>
        
        <button class="test-btn" onclick="testCloseButtonFix()">
            🧪 Test Correzione Pulsante
        </button>
        
        <button class="test-btn" onclick="location.reload()">
            🔄 Ricarica Pagina
        </button>
        
        <div id="status-box" class="status-box">
            <strong>📊 Status Test:</strong><br>
            Pronto per test correzione...
        </div>
        
        <div class="info-box">
            <h3>🎯 Test Automatico:</h3>
            <ol>
                <li><strong>Carica Modal</strong> - Con stili CSS forzati</li>
                <li><strong>Test Click Pulsante X</strong> - Deve chiudere il modal</li>
                <li><strong>Verifica Chiusura</strong> - Modal deve sparire completamente</li>
                <li><strong>Test ESC</strong> - Riapri e prova ESC</li>
            </ol>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript del Modal -->
    <script src="assets/js/modal.js"></script>
    
    <script>
        // Simula dati ASDP per il test
        window.asdpData = {
            coordinates: "42.3601° N, 13.3995° E",
            seismic_zone: "Zona 1",
            soil_category: "Categoria A",
            ag: "0.261g",
            f0: "2.47",
            tc_star: "0.31s",
            damping: "5%",
            q0_factor: "3.0"
        };
        
        let statusBox = document.getElementById('status-box');
        
        function logStatus(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : '📝';
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            
            statusBox.innerHTML += `<br><span class="${className}">${icon} ${timestamp}: ${message}</span>`;
            statusBox.scrollTop = statusBox.scrollHeight;
            
            console.log(`${icon} ${message}`);
        }
        
        function testCloseButtonFix() {
            logStatus('🔧 Test correzione pulsante chiusura...');
            
            // Test 1: Caricamento Modal Statico
            logStatus('📥 Caricamento modal statico...');
            fetch('modal_static.html')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modal-container').innerHTML = html;
                    logStatus('Modal HTML statico caricato', 'success');
                    
                    // Test 2: Inizializzazione
                    if (typeof initInertialMassModal === 'function') {
                        logStatus('Funzione initInertialMassModal trovata', 'success');
                        
                        // Test 3: Apertura Modal
                        try {
                            initInertialMassModal(window.asdpData);
                            logStatus('Modal inizializzato con stili CSS forzati', 'success');
                            
                            // Test 4: Verifica Modal Visibile
                            setTimeout(() => {
                                const modal = document.getElementById('inertialMassModal');
                                if (modal && modal.style.display === 'flex') {
                                    logStatus('Modal visibile con CSS forzato', 'success');
                                    
                                    // Test 5: Click Pulsante X
                                    const closeButton = document.querySelector('.modal-close');
                                    if (closeButton) {
                                        logStatus('Pulsante X trovato, simulando click...', 'warning');
                                        
                                        // Click programmatico
                                        closeButton.click();
                                        
                                        // Verifica chiusura dopo 1 secondo
                                        setTimeout(() => {
                                            const modalAfterClick = document.getElementById('inertialMassModal');
                                            if (modalAfterClick && modalAfterClick.style.display === 'none') {
                                                logStatus('🎉 SUCCESSO! Pulsante X funziona!', 'success');
                                                logStatus('Modal chiuso correttamente', 'success');
                                                
                                                // Test 6: Test ESC Key
                                                setTimeout(() => {
                                                    testEscKey();
                                                }, 1000);
                                                
                                            } else {
                                                logStatus('❌ FALLIMENTO! Modal ancora visibile', 'error');
                                                logStatus('Display attuale: ' + (modalAfterClick ? modalAfterClick.style.display : 'N/A'), 'error');
                                            }
                                        }, 1000);
                                        
                                    } else {
                                        logStatus('Pulsante X non trovato', 'error');
                                    }
                                } else {
                                    logStatus('Modal non visibile dopo inizializzazione', 'error');
                                }
                            }, 1000);
                            
                        } catch (error) {
                            logStatus('Errore inizializzazione: ' + error.message, 'error');
                        }
                    } else {
                        logStatus('Funzione initInertialMassModal NON trovata', 'error');
                    }
                })
                .catch(error => {
                    logStatus('Errore caricamento: ' + error.message, 'error');
                });
        }
        
        function testEscKey() {
            logStatus('⌨️ Test tasto ESC...');
            
            // Riapri il modal
            if (typeof initInertialMassModal === 'function') {
                initInertialMassModal(window.asdpData);
                logStatus('Modal riaperto per test ESC', 'success');
                
                setTimeout(() => {
                    // Simula pressione ESC
                    const escEvent = new KeyboardEvent('keydown', {
                        key: 'Escape',
                        code: 'Escape',
                        keyCode: 27,
                        which: 27,
                        bubbles: true
                    });
                    
                    document.dispatchEvent(escEvent);
                    logStatus('Evento ESC simulato', 'warning');
                    
                    // Verifica chiusura
                    setTimeout(() => {
                        const modal = document.getElementById('inertialMassModal');
                        if (modal && modal.style.display === 'none') {
                            logStatus('🎉 SUCCESSO! Tasto ESC funziona!', 'success');
                        } else {
                            logStatus('❌ Tasto ESC non funziona', 'error');
                        }
                        
                        logStatus('🏁 Test completato!', 'warning');
                    }, 500);
                }, 500);
            }
        }
        
        // Auto-inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            logStatus('🔧 Test Correzione Pulsante caricato');
        });
    </script>
</body>
</html>
