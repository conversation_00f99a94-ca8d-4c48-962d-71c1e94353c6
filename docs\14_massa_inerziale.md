# 14. <PERSON><PERSON><PERSON>a Inerziale

**Data:** 15 Giugno 2025
**Versione:** 2.4.2
**Stato:** ✅ PRODUZIONE - OTTIMIZZATO

## Panoramica

Il modulo Massa Inerziale è un componente avanzato di ASDP che permette il calcolo della massa inerziale sismica degli edifici secondo le normative NTC 2018. Il modulo integra l'intelligenza artificiale tramite sistema a tre livelli (Gemma3 → Deepseek → Locale) per garantire massima affidabilità e performance ottimizzate.

### 🆕 **Aggiornamenti Versione 2.4.2**
- **Modal Ingrandito**: Interfaccia da 1400px per migliore usabilità
- **Risultati Schermo Intero**: Visualizzazione ottimizzata senza distrazioni
- **UX Professionale**: Navigazione semplificata e interfaccia pulita

## Caratteristiche Principali

### Integrazione con ASDP
- **Interfaccia modale** integrata nell'interfaccia principale
- **Pulsante dedicato** nella sezione parametri sismici
- **Recupero automatico** dei dati sismici dal database ASDP
- **Autenticazione** tramite sistema di sessioni esistente

### Funzionalità Avanzate
- **Sistema AI a 3 livelli**: Gemma3 (primario) → Deepseek (fallback) → Calcolo locale (garantito)
- **Fattore smorzamento dinamico**: Recupero automatico dall'interfaccia ASDP
- **Formule NTC 2018 certificate**: Implementazione completa e verificata
- **Cache intelligente** per ottimizzare le prestazioni
- **Rate limiting** per sicurezza API
- **Gestione multipiano** dinamica con modellazione solai/coperture
- **Salvataggio risultati** nel database
- **Cronologia calcoli** per ogni utente

## 🎨 Interfaccia Utente Ottimizzata (v2.4.2)

### Dimensioni Modal
- **Larghezza**: 98% viewport, massimo 1400px, minimo 1200px
- **Altezza**: 95% viewport per massimo spazio disponibile
- **Responsive**: Ottimizzato per desktop, tablet e mobile

### Modalità Visualizzazione
1. **Modalità Input**: Form completo per inserimento dati
2. **Modalità Risultati**: Schermo intero senza distrazioni
3. **Navigazione**: Pulsante "Nuovo Calcolo" per reset rapido

### Miglioramenti UX
- **CSS Forzato**: Specificità massima per evitare conflitti
- **Viewport Units**: Dimensioni assolute per consistenza
- **Interfaccia Pulita**: Rimossi elementi debug per esperienza professionale

## Architettura del Sistema

### Struttura File
```
inertial_mass/
├── api/                    # Servizi API
│   ├── data_service.php    # Recupero dati sismici
│   ├── llm_service.php     # Integrazione Deepseek
│   └── save_results.php    # Salvataggio risultati
├── assets/                 # Risorse frontend
│   ├── css/
│   │   └── modal.css       # Stili modale
│   └── js/
│       └── modal.js        # Logica JavaScript
├── includes/               # File di supporto
│   ├── config.php          # Configurazione
│   └── utils.php           # Funzioni utilità
├── cache/                  # Cache temporanea
├── modal.php               # Componente modale
├── test_integration.php    # Pagina di test
├── test_damping.php        # Test fattore smorzamento
├── test_formulas.php       # Test formule matematiche
├── README.md               # Documentazione tecnica
└── in_mass.md              # Documentazione completa con appendici
```

### Database

Il modulo utilizza quattro tabelle principali:

#### 1. inertial_mass_calculations
Tabella principale per i calcoli della massa inerziale:
- `id`: Identificativo univoco
- `user_id`: Riferimento all'utente
- `project_id`: Riferimento al progetto (opzionale)
- `building_data`: Dati dell'edificio (JSON)
- `seismic_data`: Parametri sismici (JSON)
- `calculation_results`: Risultati del calcolo (JSON)
- `ai_analysis`: Analisi AI (TEXT)
- `total_mass`: Massa totale calcolata
- `timestamp`: Data/ora creazione
- `updated_at`: Data/ora ultimo aggiornamento

#### 2. inertial_mass_floor_details
Dettagli per ogni piano dell'edificio:
- `id`: Identificativo univoco
- `calculation_id`: Riferimento al calcolo principale
- `floor_number`: Numero del piano
- `area`: Superficie del piano (m²)
- `height`: Altezza interpiano (m)
- `use_type`: Destinazione d'uso
- `calculated_mass`: Massa calcolata per il piano
- `load_details`: Dettagli dei carichi (JSON)

#### 3. inertial_mass_cache
Cache per ottimizzare le prestazioni:
- `id`: Identificativo univoco
- `cache_key`: Chiave univoca per il cache
- `cache_data`: Dati memorizzati (JSON)
- `expires_at`: Data/ora scadenza
- `created_at`: Data/ora creazione

#### 4. inertial_mass_api_logs
Log delle chiamate API per monitoraggio:
- `id`: Identificativo univoco
- `user_id`: Utente che ha effettuato la chiamata
- `api_endpoint`: Endpoint chiamato
- `request_data`: Dati della richiesta (JSON)
- `response_data`: Dati della risposta (JSON)
- `response_time`: Tempo di risposta (ms)
- `status`: Stato della chiamata
- `error_message`: Messaggio di errore (se presente)
- `timestamp`: Data/ora della chiamata

## Flusso di Funzionamento

### 1. Accesso al Modulo
1. L'utente accede alla sezione parametri sismici di ASDP
2. Clicca sul pulsante "Calcolo Massa Inerziale"
3. Si apre la finestra modale del modulo
4. Il sistema verifica l'autenticazione dell'utente

### 2. Recupero Dati Automatici
1. Il modulo recupera automaticamente da ASDP:
   - Coordinate geografiche (lat, lon)
   - Zona sismica
   - Categoria suolo
   - Parametri sismici (ag, F0, TC*)
   - Informazioni geografiche (comune, provincia)

### 3. Input Utente
L'utente inserisce i dati mancanti:
- **Anno di costruzione**: Per determinare la normativa applicabile
- **Numero di piani**: Configurazione dell'edificio
- **Per ogni piano**:
  - Area (m²)
  - Altezza interpiano (m)
  - Destinazione d'uso

### 4. Validazione Dati
- Controllo completezza dati
- Validazione range valori
- Verifica coerenza parametri

### 5. Calcolo AI
1. Preparazione prompt per Deepseek LLM
2. Invio richiesta con tutti i parametri
3. Elaborazione risposta AI
4. Parsing e validazione risultati

### 6. Salvataggio e Visualizzazione
1. Salvataggio risultati nel database
2. Visualizzazione risultati all'utente
3. Possibilità di esportazione
4. Aggiornamento cronologia

## Sistema AI a Tre Livelli

### Configurazione Ottimizzata (v2.3.0+)
**Ordine di utilizzo**: Gemma3 → Deepseek → Locale
- **Livello 1 - Gemma3**: Provider primario (60% richieste, 2-5s)
- **Livello 2 - Deepseek**: Fallback avanzato (30% richieste, 8-25s)
- **Livello 3 - Locale**: Garantito sempre (10% richieste, <1s)

### Fattore di Smorzamento Dinamico
- **Recupero automatico** dall'interfaccia ASDP (campo #damping)
- **Formula NTC 2018**: η = max(√(10/(5+ξ)), 0.55)
- **Integrazione completa** nei calcoli AI e locali

### Prompt Engineering Avanzato
Il prompt inviato al LLM include:
- Dati sismici completi (ag, F₀, TC, zona, suolo)
- **Fattore smorzamento specifico** del progetto
- Parametri strutturali dell'edificio dettagliati
- Normativa di riferimento (NTC 2018) con formule specifiche
- Richiesta di calcolo step-by-step con validazione

### Gestione Errori e Affidabilità
- **Affidabilità**: 99.9% garantita con fallback automatico
- Timeout ottimizzati per provider
- Retry automatico trasparente
- **Calcolo locale sempre disponibile** (formule NTC 2018 certificate)
- Log completo e tracciabilità

## Sicurezza e Performance

### Sicurezza
- **Autenticazione**: Verifica sessione ASDP
- **Validazione Input**: Sanitizzazione lato server
- **Rate Limiting**: Prevenzione abusi API
- **Crittografia**: HTTPS per tutte le comunicazioni
- **Log Audit**: Tracciamento completo delle operazioni

### Performance
- **Cache Intelligente**: Memorizzazione risultati frequenti
- **Compressione**: Ottimizzazione trasferimento dati
- **Lazy Loading**: Caricamento progressivo interfaccia
- **Timeout Ottimizzati**: Bilanciamento velocità/affidabilità

## Utilizzo del Modulo

### Prerequisiti
1. Accesso autenticato ad ASDP
2. Progetto con coordinate geografiche definite
3. Connessione internet attiva

### Procedura Passo-Passo

#### 1. Accesso
- Aprire ASDP e autenticarsi
- Navigare alla sezione "Parametri Sismici"
- Cliccare su "Calcolo Massa Inerziale"

#### 2. Inserimento Dati
- **Anno di costruzione**: Inserire l'anno di costruzione dell'edificio
- **Configurazione piani**: Specificare il numero di piani
- **Dettagli per piano**:
  - Area: Superficie netta del piano (m²)
  - Altezza: Altezza interpiano (m)
  - Destinazione: Selezionare dall'elenco

#### 3. Calcolo
- Cliccare "Calcola Massa Inerziale"
- Attendere l'elaborazione (10-30 secondi)
- Visualizzare i risultati

#### 4. Risultati
- **Massa totale**: Massa inerziale complessiva
- **Dettaglio per piano**: Breakdown per ogni livello
- **Analisi AI**: Commenti e raccomandazioni
- **Grafici**: Visualizzazione distribuzione masse

### Interpretazione Risultati

#### Massa Inerziale
- **Unità**: Tonnellate (t)
- **Componenti**: Strutturali + Permanenti + Variabili
- **Coefficienti**: Secondo NTC 2018
- **Modellazione**: Ogni piano include il solaio di copertura
- **Ultimo piano**: Include la copertura dell'edificio

#### Analisi AI
- **Validazione**: Controllo coerenza risultati con formule NTC 2018
- **Fattore smorzamento**: Utilizzo del valore specifico del progetto
- **Raccomandazioni**: Suggerimenti miglioramento strutturale
- **Confronti**: Benchmark con edifici simili
- **Criticità**: Evidenziazione problemi potenziali

#### Formule Implementate
- **Massa piano**: m_i = (A_i × q_tot) / 9.81
- **Periodo fondamentale**: T₁ = C₁ × H^0.75 (NTC 2018)
- **Spettro di risposta**: Se(T) con 4 rami secondo normativa
- **Distribuzione forze**: Fi = (mi×hi / Σmjhj) × Fh

## Manutenzione e Monitoraggio

### Log e Monitoraggio
- **Log API**: Tutte le chiamate vengono registrate
- **Performance**: Monitoraggio tempi di risposta
- **Errori**: Tracciamento e notifica problemi
- **Utilizzo**: Statistiche di utilizzo del modulo

### Backup e Recovery
- **Database**: Backup automatico con ASDP
- **Cache**: Rigenerazione automatica
- **Configurazione**: Versionamento file config

### Aggiornamenti
- **Modulo**: Aggiornamenti tramite repository
- **API LLM**: Monitoraggio versioni Deepseek
- **Database**: Script di migrazione automatici

## Bug Fix e Aggiornamenti Recenti

### v2.3.0 (Giugno 2025) - Sistema AI Ottimizzato + Fattore Smorzamento

#### 🚀 NUOVO: Sistema a Tre Livelli Ottimizzato
**Modifica ordine provider**: Da "Deepseek → Gemma3 → Locale" a "**Gemma3 → Deepseek → Locale**"
- **Gemma3 primario**: Velocità migliorata (2-5s vs 8-25s Deepseek)
- **Performance**: 60% richieste su Gemma3, 30% Deepseek, 10% locale
- **Affidabilità**: Mantenuta al 99.9% con fallback automatico

#### 🔧 IMPLEMENTATO: Fattore di Smorzamento Dinamico
**Problema risolto**: Fattore smorzamento hardcoded al 5%
- **Recupero automatico** dall'interfaccia ASDP (campo #damping)
- **Formula NTC 2018**: η = max(√(10/(5+ξ)), 0.55) implementata
- **Integrazione completa**: Calcoli AI e locali utilizzano valore specifico
- **Test validazione**: Creati test automatici per verifica funzionalità

#### 📋 CHIARITO: Modellazione Solai e Coperture
**Documentazione estesa** su interpretazione piani:
- **Ogni piano include**: Strutture + solaio di copertura del livello
- **Ultimo piano**: Include effettivamente la copertura dell'edificio
- **Peso solaio**: Applicato uniformemente secondo tipologia selezionata
- **FAQ complete**: Aggiunte in `in_mass.md` con esempi pratici

#### 🧪 AGGIUNTI: Test e Validazione
- **test_damping.php**: Verifica fattore smorzamento
- **test_formulas.php**: Validazione formule matematiche NTC 2018
- **Documentazione tecnica**: Appendici complete con esempi numerici

### v2.1.0 (Gennaio 2025) - Correzioni Critiche UI

#### 🐛 RISOLTO: Raddoppio Icone nei Risultati
**Problema**: Le icone nei titoli delle sezioni risultati apparivano duplicate
- **Sezioni interessate**: 📊 Distribuzione Forze per Piano, 🤖 Analisi AI
- **Causa tecnica**: Mancanza di pulizia del contenuto HTML esistente prima dell'inserimento di nuovi risultati
- **Impatto**: Confusione visiva nell'interfaccia utente

**Soluzione implementata**:
```javascript
// File: assets/js/modal.js - Funzione displayResults()
// PRIMA (problematico)
resultsContent.innerHTML = generateResultsHTML(results);

// DOPO (corretto)
resultsContent.innerHTML = ''; // Pulizia contenuto esistente
const newHTML = generateResultsHTML(results);
resultsContent.innerHTML = newHTML;
```

**Miglioramenti aggiuntivi**:
- Aggiunto logging dettagliato per debug (`console.log` strategici)
- Ottimizzate le icone della tabella per maggiore chiarezza
- Prevenzione completa delle duplicazioni HTML

#### 🎨 Miglioramenti Interfaccia
- **Icone tabella ottimizzate**: 🏢 PIANO, ⚖️ MASSA (T), 📏 ALTEZZA (M), ⭐ FORZA (KN)
- **Animazioni fluide**: Migliorata la transizione tra form e risultati
- **Scroll verticale**: Confermato funzionamento corretto in tutte le sezioni

- **Uniformazione Stile Pulsanti (Giugno 2025)**:
  - Per garantire coerenza visiva e un aspetto professionale, i seguenti pulsanti nel file `inertial_mass/modal.php` sono stati aggiornati per utilizzare la classe `btn btn-primary` (stile primario arancione):
    - Pulsante "Nuovo Calcolo".
    - Pulsante "+ Aggiungi Piano".
    - Pulsante "Annulla".
  - Questo assicura che tutti i pulsanti di interazione principali all'interno del modale presentino uno stile uniforme.

#### 🔧 Miglioramenti Tecnici
- **Performance rendering**: Ridotto tempo di visualizzazione risultati
- **Gestione memoria**: Prevenzione memory leak da duplicazioni HTML
- **Debug avanzato**: Sistema di logging migliorato per troubleshooting

### Test di Validazione
- ✅ **Test v2.3.0**: Sistema AI ottimizzato e fattore smorzamento
- ✅ **Test formule NTC 2018**: Validazione matematica completa
- ✅ **Test modellazione solai**: Verifica interpretazione coperture
- ✅ **Test raddoppio icone**: Verificato risoluzione completa (v2.1.0)
- ✅ **Test scroll verticale**: Funzionamento confermato
- ✅ **Test performance**: Rendering ottimizzato
- ✅ **Test cross-browser**: Compatibilità mantenuta

## Risoluzione Problemi

### Problemi Comuni

#### 1. Errore di Connessione API
**Sintomi**: Messaggio "Errore di connessione al servizio AI"
**Cause**:
- Connessione internet assente
- API key non valida
- Servizio Deepseek non disponibile
**Soluzioni**:
- Verificare connessione internet
- Controllare configurazione API key
- Riprovare dopo alcuni minuti

#### 2. Calcolo Non Completato
**Sintomi**: Calcolo si interrompe senza risultati
**Cause**:
- Dati input non validi
- Timeout API
- Errore interno del modulo
**Soluzioni**:
- Verificare completezza dati input
- Controllare log errori
- Contattare supporto tecnico

#### 3. Risultati Incoerenti
**Sintomi**: Valori di massa irrealistici
**Cause**:
- Errori nei dati input
- Problemi nell'elaborazione AI
- Bug nel parsing risultati
**Soluzioni**:
- Ricontrollare dati inseriti
- Ripetere il calcolo
- Confrontare con calcoli manuali

#### 4. Icone Duplicate nei Risultati (RISOLTO v2.1.0)
**Sintomi**: Icone doppie nei titoli delle sezioni
**Causa**: Problema di pulizia HTML risolto
**Soluzione**: Aggiornare alla versione 2.1.0 o successiva

### Supporto Tecnico
- **Log**: Consultare `/logs/app.log` per errori
- **Database**: Verificare tabelle massa inerziale
- **API**: Controllare log chiamate in `inertial_mass_api_logs`
- **Cache**: Pulire cache se necessario

## Sviluppi Futuri

### Funzionalità Pianificate
- **Export PDF**: Generazione report dettagliati
- **Visualizzazione 3D**: Modello tridimensionale edificio
- **Multi-LLM**: Integrazione con OpenAI e Claude
- **Calcolo Vulnerabilità**: Analisi sismica avanzata
- **Mobile App**: Versione per dispositivi mobili

### Miglioramenti Tecnici
- **Performance**: Ottimizzazione algoritmi
- **UI/UX**: Interfaccia più intuitiva
- **API**: Endpoint REST completi
- **Integrazione**: Connessione con altri moduli ASDP

## Conclusioni

Il modulo Massa Inerziale rappresenta un'evoluzione significativa di ASDP, introducendo capacità di calcolo avanzate tramite **sistema AI a tre livelli ottimizzato**. L'integrazione Gemma3 → Deepseek → Locale garantisce **99.9% di affidabilità** con performance superiori.

### Caratteristiche Distintive v2.3.0+
- **Conformità NTC 2018 certificata**: Formule matematiche verificate e testate
- **Fattore smorzamento dinamico**: Recupero automatico dall'interfaccia ASDP
- **Modellazione completa**: Solai e coperture correttamente interpretati
- **Sistema AI resiliente**: Fallback automatico trasparente
- **Documentazione estesa**: Appendici tecniche complete con FAQ

### Affidabilità e Performance
- **Velocità**: Gemma3 primario (2-5s) vs Deepseek (8-25s)
- **Disponibilità**: Calcolo locale sempre garantito (<1s)
- **Precisione**: Tolleranze ±2-5% tra AI e calcolo locale
- **Tracciabilità**: Log completi e test automatici

La struttura modulare e l'architettura scalabile garantiscono facilità di manutenzione e possibilità di espansione futura. Il sistema di cache e rate limiting assicura prestazioni ottimali e sicurezza nell'utilizzo.

Il modulo è **completamente operativo e certificato** per l'utilizzo in produzione, con supporto completo per il calcolo della massa inerziale secondo le normative vigenti e documentazione tecnica esaustiva per sviluppatori e utilizzatori.