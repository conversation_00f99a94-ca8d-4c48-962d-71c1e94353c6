/**
 * debug_close_button.js - Sistema di log per debug pulsante chiusura
 * Path: /inertial_mass/logs/debug_close_button.js
 */

// Sistema di log avanzato
class ModalDebugLogger {
    constructor() {
        this.logs = [];
        this.startTime = Date.now();
        this.logContainer = null;
        this.init();
    }

    init() {
        // Crea container per i log
        this.createLogContainer();
        
        // Intercetta tutti i click
        this.interceptAllClicks();
        
        // Intercetta tutte le funzioni del modal
        this.interceptModalFunctions();
        
        // Log iniziale
        this.log('🚀 SISTEMA DEBUG INIZIALIZZATO', 'system');
    }

    createLogContainer() {
        // Rimuovi container esistente
        const existing = document.getElementById('debug-log-container');
        if (existing) existing.remove();

        // Crea nuovo container
        const container = document.createElement('div');
        container.id = 'debug-log-container';
        container.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            max-height: 80vh;
            background: #000;
            color: #0f0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 10px;
            border: 2px solid #0f0;
            border-radius: 5px;
            z-index: 999999;
            overflow-y: auto;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        `;

        const header = document.createElement('div');
        header.innerHTML = `
            <strong>🔍 MODAL DEBUG LOG</strong>
            <button onclick="this.parentElement.parentElement.remove()" style="float: right; background: #f00; color: #fff; border: none; padding: 2px 6px; cursor: pointer;">X</button>
            <button onclick="debugLogger.clearLogs()" style="float: right; background: #ff0; color: #000; border: none; padding: 2px 6px; cursor: pointer; margin-right: 5px;">Clear</button>
            <hr style="border-color: #0f0;">
        `;

        const logArea = document.createElement('div');
        logArea.id = 'debug-log-area';
        logArea.style.cssText = `
            max-height: 60vh;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        `;

        container.appendChild(header);
        container.appendChild(logArea);
        document.body.appendChild(container);

        this.logContainer = logArea;
    }

    log(message, type = 'info', data = null) {
        const timestamp = new Date().toLocaleTimeString();
        const elapsed = Date.now() - this.startTime;
        
        const logEntry = {
            timestamp,
            elapsed,
            type,
            message,
            data,
            stack: new Error().stack
        };

        this.logs.push(logEntry);

        // Colori per tipo
        const colors = {
            system: '#0ff',
            click: '#ff0',
            function: '#f0f',
            error: '#f00',
            success: '#0f0',
            warning: '#fa0',
            info: '#fff'
        };

        const color = colors[type] || '#fff';
        
        let logText = `[${timestamp}] [+${elapsed}ms] `;
        
        switch(type) {
            case 'system': logText += '🚀 '; break;
            case 'click': logText += '👆 '; break;
            case 'function': logText += '⚙️ '; break;
            case 'error': logText += '❌ '; break;
            case 'success': logText += '✅ '; break;
            case 'warning': logText += '⚠️ '; break;
            default: logText += '📝 '; break;
        }
        
        logText += message;
        
        if (data) {
            logText += '\n   📊 Data: ' + JSON.stringify(data, null, 2);
        }

        // Aggiungi al container visuale
        if (this.logContainer) {
            const logDiv = document.createElement('div');
            logDiv.style.color = color;
            logDiv.style.marginBottom = '5px';
            logDiv.style.borderLeft = `3px solid ${color}`;
            logDiv.style.paddingLeft = '5px';
            logDiv.textContent = logText;
            
            this.logContainer.appendChild(logDiv);
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
        }

        // Log anche in console
        console.log(`%c${logText}`, `color: ${color}; font-weight: bold;`, data || '');

        // Salva su file ogni 10 log
        if (this.logs.length % 10 === 0) {
            this.saveToFile();
        }
    }

    clearLogs() {
        this.logs = [];
        if (this.logContainer) {
            this.logContainer.innerHTML = '';
        }
        this.log('🧹 LOG PULITI', 'system');
    }

    interceptAllClicks() {
        // Intercetta TUTTI i click sulla pagina
        document.addEventListener('click', (e) => {
            const target = e.target;
            const tagName = target.tagName;
            const className = target.className;
            const id = target.id;
            const onclick = target.getAttribute('onclick');
            const type = target.type;

            this.log(`CLICK INTERCETTATO`, 'click', {
                tagName,
                className,
                id,
                onclick,
                type,
                textContent: target.textContent?.substring(0, 50),
                parentElement: target.parentElement?.tagName,
                isModalClose: target.classList.contains('modal-close') || onclick?.includes('closeInertialMassModal')
            });

            // Se è un pulsante di chiusura, log speciale
            if (target.classList.contains('modal-close') || onclick?.includes('closeInertialMassModal')) {
                this.log('🎯 PULSANTE CHIUSURA CLICCATO!', 'warning', {
                    element: target.outerHTML,
                    computedStyle: window.getComputedStyle(target),
                    eventListeners: this.getEventListeners(target)
                });
            }
        }, true); // true = capture phase
    }

    interceptModalFunctions() {
        // Intercetta closeInertialMassModal
        if (typeof window.closeInertialMassModal === 'function') {
            const originalClose = window.closeInertialMassModal;
            window.closeInertialMassModal = (...args) => {
                this.log('🔴 closeInertialMassModal CHIAMATA', 'function', {
                    arguments: args,
                    stackTrace: new Error().stack
                });
                
                try {
                    const result = originalClose.apply(this, args);
                    this.log('✅ closeInertialMassModal COMPLETATA', 'success');
                    return result;
                } catch (error) {
                    this.log('❌ closeInertialMassModal ERRORE', 'error', error);
                    throw error;
                }
            };
        }

        // Intercetta resetCalculation
        if (typeof window.resetCalculation === 'function') {
            const originalReset = window.resetCalculation;
            window.resetCalculation = (...args) => {
                this.log('🔄 resetCalculation CHIAMATA', 'function', {
                    arguments: args,
                    stackTrace: new Error().stack
                });
                
                try {
                    const result = originalReset.apply(this, args);
                    this.log('✅ resetCalculation COMPLETATA', 'success');
                    return result;
                } catch (error) {
                    this.log('❌ resetCalculation ERRORE', 'error', error);
                    throw error;
                }
            };
        }

        // Intercetta initInertialMassModal
        if (typeof window.initInertialMassModal === 'function') {
            const originalInit = window.initInertialMassModal;
            window.initInertialMassModal = (...args) => {
                this.log('🚀 initInertialMassModal CHIAMATA', 'function', {
                    arguments: args
                });
                
                try {
                    const result = originalInit.apply(this, args);
                    this.log('✅ initInertialMassModal COMPLETATA', 'success');
                    return result;
                } catch (error) {
                    this.log('❌ initInertialMassModal ERRORE', 'error', error);
                    throw error;
                }
            };
        }
    }

    getEventListeners(element) {
        // Prova a ottenere event listeners (funziona solo in dev tools)
        try {
            if (typeof getEventListeners === 'function') {
                return getEventListeners(element);
            }
        } catch (e) {
            // Fallback: controlla proprietà comuni
            return {
                onclick: element.onclick,
                addEventListener: 'Non disponibile in produzione'
            };
        }
        return null;
    }

    saveToFile() {
        // Crea contenuto del file di log
        const logContent = this.logs.map(log => {
            return `[${log.timestamp}] [+${log.elapsed}ms] [${log.type.toUpperCase()}] ${log.message}` +
                   (log.data ? '\nDATA: ' + JSON.stringify(log.data, null, 2) : '') +
                   '\nSTACK: ' + log.stack + '\n' + '='.repeat(80) + '\n';
        }).join('\n');

        // Crea e scarica file
        const blob = new Blob([logContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `modal_debug_${new Date().toISOString().replace(/[:.]/g, '-')}.log`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.log('💾 LOG SALVATO SU FILE', 'system');
    }

    // Metodo per analizzare lo stato del modal
    analyzeModalState() {
        const modal = document.getElementById('inertialMassModal');
        if (!modal) {
            this.log('❌ MODAL NON TROVATO', 'error');
            return;
        }

        const computedStyle = window.getComputedStyle(modal);
        const closeButtons = modal.querySelectorAll('.modal-close, [onclick*="closeInertialMassModal"]');

        this.log('🔍 ANALISI STATO MODAL', 'info', {
            modalExists: !!modal,
            display: computedStyle.display,
            visibility: computedStyle.visibility,
            opacity: computedStyle.opacity,
            zIndex: computedStyle.zIndex,
            position: computedStyle.position,
            closeButtonsFound: closeButtons.length,
            closeButtonsDetails: Array.from(closeButtons).map(btn => ({
                tagName: btn.tagName,
                className: btn.className,
                onclick: btn.getAttribute('onclick'),
                disabled: btn.disabled,
                style: btn.style.cssText
            }))
        });
    }
}

// Inizializza il logger globalmente
window.debugLogger = new ModalDebugLogger();

// Funzioni di utilità globali
window.analyzeModal = () => window.debugLogger.analyzeModalState();
window.clearDebugLogs = () => window.debugLogger.clearLogs();
window.saveDebugLogs = () => window.debugLogger.saveToFile();

console.log('🔍 SISTEMA DEBUG MODAL CARICATO - Usa analyzeModal(), clearDebugLogs(), saveDebugLogs()');
