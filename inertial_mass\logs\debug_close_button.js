/**
 * debug_close_button.js - Sistema di log per debug pulsante chiusura
 * Path: /inertial_mass/logs/debug_close_button.js
 */

// Sistema di log avanzato
class ModalDebugLogger {
    constructor() {
        this.logs = [];
        this.startTime = Date.now();
        this.logContainer = null;
        this.init();
    }

    init() {
        // Crea container per i log
        this.createLogContainer();
        
        // Intercetta tutti i click
        this.interceptAllClicks();
        
        // Intercetta tutte le funzioni del modal
        this.interceptModalFunctions();
        
        // Log iniziale
        this.log('🚀 SISTEMA DEBUG INIZIALIZZATO', 'system');
    }

    createLogContainer() {
        // Rimuovi container esistente
        const existing = document.getElementById('debug-log-container');
        if (existing) existing.remove();

        // Crea nuovo container
        const container = document.createElement('div');
        container.id = 'debug-log-container';
        container.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            max-height: 80vh;
            background: #000;
            color: #0f0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 10px;
            border: 2px solid #0f0;
            border-radius: 5px;
            z-index: 999999;
            overflow-y: auto;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        `;

        const header = document.createElement('div');
        header.innerHTML = `
            <strong>🔍 MODAL DEBUG LOG</strong>
            <button onclick="this.parentElement.parentElement.remove()" style="float: right; background: #f00; color: #fff; border: none; padding: 2px 6px; cursor: pointer;">X</button>
            <button onclick="debugLogger.clearLogs()" style="float: right; background: #ff0; color: #000; border: none; padding: 2px 6px; cursor: pointer; margin-right: 5px;">Clear</button>
            <hr style="border-color: #0f0;">
        `;

        const logArea = document.createElement('div');
        logArea.id = 'debug-log-area';
        logArea.style.cssText = `
            max-height: 60vh;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        `;

        container.appendChild(header);
        container.appendChild(logArea);
        document.body.appendChild(container);

        this.logContainer = logArea;
    }

    log(message, type = 'info', data = null) {
        const timestamp = new Date().toLocaleTimeString();

        const logEntry = {
            timestamp,
            type,
            message
        };

        this.logs.push(logEntry);

        // Colori per tipo
        const colors = {
            system: '#0ff',
            click: '#ff0',
            function: '#f0f',
            error: '#f00',
            success: '#0f0',
            warning: '#fa0',
            info: '#fff'
        };

        const color = colors[type] || '#fff';

        let logText = `[${timestamp}] `;

        switch(type) {
            case 'system': logText += '🚀 '; break;
            case 'click': logText += '👆 '; break;
            case 'function': logText += '⚙️ '; break;
            case 'error': logText += '❌ '; break;
            case 'success': logText += '✅ '; break;
            case 'warning': logText += '⚠️ '; break;
            default: logText += '📝 '; break;
        }

        logText += message;

        // Aggiungi al container visuale
        if (this.logContainer) {
            const logDiv = document.createElement('div');
            logDiv.style.color = color;
            logDiv.style.marginBottom = '3px';
            logDiv.style.borderLeft = `2px solid ${color}`;
            logDiv.style.paddingLeft = '5px';
            logDiv.style.fontSize = '11px';
            logDiv.textContent = logText;

            this.logContainer.appendChild(logDiv);
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
        }

        // Log anche in console
        console.log(`%c${logText}`, `color: ${color}; font-weight: bold;`);
    }

    clearLogs() {
        this.logs = [];
        if (this.logContainer) {
            this.logContainer.innerHTML = '';
        }
        this.log('🧹 LOG PULITI', 'system');
    }

    interceptAllClicks() {
        // Intercetta SOLO i click sui pulsanti di chiusura del modal
        document.addEventListener('click', (e) => {
            const target = e.target;
            const onclick = target.getAttribute('onclick');

            // Log SOLO se è un pulsante di chiusura o test
            const isModalClose = target.classList.contains('modal-close') || onclick?.includes('closeInertialMassModal');
            const isTestButton = onclick?.includes('testCloseButtonFix');

            if (isModalClose || isTestButton) {
                this.log(`CLICK: ${target.tagName} - ${onclick || target.className}`, 'click');

                // Se è pulsante chiusura, log speciale
                if (isModalClose) {
                    this.log('🎯 PULSANTE CHIUSURA CLICCATO!', 'warning');
                }
            }
        }, true);
    }

    interceptModalFunctions() {
        // Intercetta closeInertialMassModal
        if (typeof window.closeInertialMassModal === 'function') {
            const originalClose = window.closeInertialMassModal;
            window.closeInertialMassModal = (...args) => {
                this.log('🔴 closeInertialMassModal CHIAMATA', 'function', {
                    arguments: args,
                    stackTrace: new Error().stack
                });
                
                try {
                    const result = originalClose.apply(this, args);
                    this.log('✅ closeInertialMassModal COMPLETATA', 'success');
                    return result;
                } catch (error) {
                    this.log('❌ closeInertialMassModal ERRORE', 'error', error);
                    throw error;
                }
            };
        }

        // Intercetta resetCalculation
        if (typeof window.resetCalculation === 'function') {
            const originalReset = window.resetCalculation;
            window.resetCalculation = (...args) => {
                this.log('🔄 resetCalculation CHIAMATA', 'function', {
                    arguments: args,
                    stackTrace: new Error().stack
                });
                
                try {
                    const result = originalReset.apply(this, args);
                    this.log('✅ resetCalculation COMPLETATA', 'success');
                    return result;
                } catch (error) {
                    this.log('❌ resetCalculation ERRORE', 'error', error);
                    throw error;
                }
            };
        }

        // Intercetta initInertialMassModal
        if (typeof window.initInertialMassModal === 'function') {
            const originalInit = window.initInertialMassModal;
            window.initInertialMassModal = (...args) => {
                this.log('🚀 initInertialMassModal CHIAMATA', 'function', {
                    arguments: args
                });
                
                try {
                    const result = originalInit.apply(this, args);
                    this.log('✅ initInertialMassModal COMPLETATA', 'success');
                    return result;
                } catch (error) {
                    this.log('❌ initInertialMassModal ERRORE', 'error', error);
                    throw error;
                }
            };
        }
    }

    getEventListeners(element) {
        // Versione semplificata - solo onclick
        return {
            onclick: element.getAttribute('onclick') || 'nessuno'
        };
    }

    saveToFile() {
        // Crea contenuto del file di log
        const logContent = this.logs.map(log => {
            return `[${log.timestamp}] [+${log.elapsed}ms] [${log.type.toUpperCase()}] ${log.message}` +
                   (log.data ? '\nDATA: ' + JSON.stringify(log.data, null, 2) : '') +
                   '\nSTACK: ' + log.stack + '\n' + '='.repeat(80) + '\n';
        }).join('\n');

        // Crea e scarica file
        const blob = new Blob([logContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `modal_debug_${new Date().toISOString().replace(/[:.]/g, '-')}.log`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.log('💾 LOG SALVATO SU FILE', 'system');
    }

    // Metodo per analizzare lo stato del modal
    analyzeModalState() {
        const modal = document.getElementById('inertialMassModal');
        if (!modal) {
            this.log('❌ MODAL NON TROVATO', 'error');
            return;
        }

        const computedStyle = window.getComputedStyle(modal);
        const closeButtons = modal.querySelectorAll('.modal-close, [onclick*="closeInertialMassModal"]');

        this.log('🔍 MODAL ESISTE: SI', 'info');
        this.log(`📺 DISPLAY: ${computedStyle.display}`, 'info');
        this.log(`👁️ VISIBILITY: ${computedStyle.visibility}`, 'info');
        this.log(`🔘 OPACITY: ${computedStyle.opacity}`, 'info');
        this.log(`🔲 PULSANTI CHIUSURA: ${closeButtons.length}`, 'info');

        closeButtons.forEach((btn, i) => {
            this.log(`   Pulsante ${i+1}: ${btn.tagName} onclick="${btn.getAttribute('onclick')}"`, 'info');
        });
    }
}

// Inizializza il logger globalmente
window.debugLogger = new ModalDebugLogger();

// Funzioni di utilità globali
window.analyzeModal = () => window.debugLogger.analyzeModalState();
window.clearDebugLogs = () => window.debugLogger.clearLogs();
window.saveDebugLogs = () => window.debugLogger.saveToFile();

console.log('🔍 SISTEMA DEBUG MODAL CARICATO - Usa analyzeModal(), clearDebugLogs(), saveDebugLogs()');
