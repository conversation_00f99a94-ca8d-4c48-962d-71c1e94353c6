<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Event Listener Fix - Modal <PERSON>a Inerziale</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CSS del Modal -->
    <link rel="stylesheet" href="assets/css/modal.css">
    
    <style>
        body {
            background-color: #121212;
            color: #f8f9fa;
            font-family: 'Segoe UI', Arial, sans-serif;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
        }
        
        .test-btn {
            background-color: #D97706;
            color: white;
            border: 1px solid #C26A05;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1.1rem;
            margin: 1rem;
            transition: all 0.2s ease;
        }
        
        .test-btn:hover {
            background-color: #C26A05;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3);
        }
        
        .test-btn.success {
            background-color: #28a745;
            border-color: #1e7e34;
        }
        
        .test-btn.success:hover {
            background-color: #1e7e34;
        }
        
        .test-btn.danger {
            background-color: #dc3545;
            border-color: #c82333;
        }
        
        .test-btn.danger:hover {
            background-color: #c82333;
        }
        
        .info-box {
            background-color: #2a2a2a;
            border: 1px solid #555e67;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: left;
        }
        
        .info-box h3 {
            color: #D97706;
            margin-bottom: 1rem;
        }
        
        .info-box ul {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.5rem;
        }
        
        .status-box {
            background-color: #1a1a1a;
            border: 2px solid #555e67;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-success {
            border-color: #28a745;
            background-color: #1a2e1a;
        }
        
        .status-error {
            border-color: #dc3545;
            background-color: #2e1a1a;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Correzione Event Listener</h1>
        <p>Test specifico per verificare che gli event listener delle tipologie costruttive funzionino correttamente</p>
        
        <div class="info-box">
            <h3>🎯 Obiettivo del Test:</h3>
            <ul>
                <li>✅ Verificare che l'event listener per categoria costruttiva si attivi</li>
                <li>✅ Controllare che le sottocategorie appaiano correttamente</li>
                <li>✅ Testare la robustezza dell'inizializzazione</li>
                <li>✅ Validare il doppio sistema di event listener</li>
            </ul>
        </div>
        
        <div class="test-grid">
            <button class="test-btn" onclick="openModal()">
                🚀 Apri Modal
            </button>
            
            <button class="test-btn success" onclick="testEventListenerDirect()">
                🧪 Test Event Listener Diretto
            </button>
            
            <button class="test-btn danger" onclick="debugEventListener()">
                🔍 Debug Event Listener
            </button>
        </div>
        
        <div id="status-box" class="status-box">
            <strong>📊 Status Test:</strong><br>
            Pronto per iniziare i test...
        </div>
        
        <div class="info-box">
            <h3>📋 Sequenza di Test Consigliata:</h3>
            <ol>
                <li><strong>Apri Modal</strong> - Verifica che il modal si apra correttamente</li>
                <li><strong>Test Event Listener Diretto</strong> - Testa programmaticamente l'event listener</li>
                <li><strong>Test Manuale</strong> - Cambia manualmente la categoria nel modal</li>
                <li><strong>Debug Event Listener</strong> - Analizza lo stato degli event listener</li>
            </ol>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript del Modal -->
    <script src="assets/js/modal.js"></script>
    
    <script>
        // Simula dati ASDP per il test
        window.asdpData = {
            coordinates: "42.3601° N, 13.3995° E",
            zone: "Zona 1",
            category: "Categoria A",
            ag: "0.261g",
            f0: "2.47",
            tc_star: "0.31s"
        };
        
        let statusBox = document.getElementById('status-box');
        
        function logStatus(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : '📝';
            
            statusBox.innerHTML += `<br>${icon} ${timestamp}: ${message}`;
            statusBox.scrollTop = statusBox.scrollHeight;
            
            // Cambia colore del box
            statusBox.className = 'status-box';
            if (type === 'success') statusBox.classList.add('status-success');
            if (type === 'error') statusBox.classList.add('status-error');
            
            console.log(`${icon} ${message}`);
        }
        
        function openModal() {
            logStatus('🔧 Apertura modal per test event listener...');
            
            // Carica il modal
            fetch('modal.php')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modal-container').innerHTML = html;
                    
                    // Inizializza il modal
                    if (typeof initializeInertialMassModal === 'function') {
                        initializeInertialMassModal();
                    }
                    
                    // Mostra il modal
                    const modal = document.getElementById('inertialMassModal');
                    if (modal) {
                        modal.style.display = 'flex';
                        logStatus('Modal aperto con successo', 'success');
                        
                        // Popola i dati automatici
                        populateSeismicData();
                        
                        // Attendi un momento e poi testa
                        setTimeout(() => {
                            logStatus('Modal pronto per test event listener');
                        }, 500);
                    } else {
                        logStatus('Modal non trovato nel DOM', 'error');
                    }
                })
                .catch(error => {
                    logStatus('Errore nel caricamento del modal: ' + error.message, 'error');
                });
        }
        
        function populateSeismicData() {
            // Popola i dati sismici automatici
            const elements = {
                'im-coordinates': window.asdpData.coordinates,
                'im-zone': window.asdpData.zone,
                'im-category': window.asdpData.category,
                'im-ag': window.asdpData.ag,
                'im-f0': window.asdpData.f0,
                'im-tc-star': window.asdpData.tc_star
            };
            
            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                    logStatus(`Popolato ${id}: ${value}`);
                }
            });
        }
        
        function testEventListenerDirect() {
            logStatus('🧪 Avvio test event listener diretto...');
            
            const categorySelect = document.getElementById('im-construction-category');
            if (!categorySelect) {
                logStatus('Elemento categoria non trovato! Apri prima il modal.', 'error');
                return;
            }
            
            logStatus('Elemento categoria trovato, eseguendo test...');
            
            // Test 1: Imposta valore "building"
            logStatus('Test 1: Impostando categoria "building"...');
            categorySelect.value = 'building';
            
            // Triggera l'evento
            const event = new Event('change', { bubbles: true, cancelable: true });
            categorySelect.dispatchEvent(event);
            
            // Verifica risultato
            setTimeout(() => {
                const structureDiv = document.getElementById('structure-subcategory');
                const slabDiv = document.getElementById('slab-subcategory');
                
                if (structureDiv && slabDiv) {
                    const structureVisible = structureDiv.style.display !== 'none';
                    const slabVisible = slabDiv.style.display !== 'none';
                    
                    if (structureVisible && slabVisible) {
                        logStatus('TEST SUCCESSO: Event listener funziona!', 'success');
                    } else {
                        logStatus('TEST FALLITO: Sottocategorie non visibili', 'error');
                        logStatus(`Struttura visibile: ${structureVisible}, Solaio visibile: ${slabVisible}`);
                    }
                } else {
                    logStatus('TEST FALLITO: Elementi sottocategoria non trovati', 'error');
                }
                
                // Reset
                categorySelect.value = '';
                categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
            }, 200);
        }
        
        function debugEventListener() {
            logStatus('🔍 Avvio debug event listener...');
            
            const categorySelect = document.getElementById('im-construction-category');
            if (!categorySelect) {
                logStatus('Elemento categoria non trovato!', 'error');
                return;
            }
            
            // Analizza lo stato dell'elemento
            logStatus(`ID elemento: ${categorySelect.id}`);
            logStatus(`Valore corrente: "${categorySelect.value}"`);
            logStatus(`Elemento visibile: ${categorySelect.offsetParent !== null}`);
            logStatus(`onchange definito: ${categorySelect.onchange !== null}`);
            
            // Verifica event listeners
            const listeners = getEventListeners ? getEventListeners(categorySelect) : 'Non disponibile';
            logStatus(`Event listeners: ${JSON.stringify(listeners)}`);
            
            // Test delle funzioni
            if (typeof handleCategoryChange === 'function') {
                logStatus('Funzione handleCategoryChange disponibile', 'success');
            } else {
                logStatus('Funzione handleCategoryChange NON disponibile', 'error');
            }
            
            if (typeof initConstructionTypeHandlers === 'function') {
                logStatus('Funzione initConstructionTypeHandlers disponibile', 'success');
                
                // Reinizializza
                logStatus('Reinizializzando event listeners...');
                initConstructionTypeHandlers();
            } else {
                logStatus('Funzione initConstructionTypeHandlers NON disponibile', 'error');
            }
        }
        
        // Auto-inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            logStatus('🔧 Test Event Listener Fix caricato');
            logStatus('📊 Dati ASDP simulati pronti');
        });
    </script>
</body>
</html>
