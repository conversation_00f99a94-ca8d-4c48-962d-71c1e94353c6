<?php
/**
 * modal.php - Punto di ingresso modale per il modulo massa inerziale
 * Path: /inertial_mass/modal.php
 */

// Verifica autenticazione
if (!isset($_SESSION)) {
    session_start();
}

// In ambiente di test, non bloccare l'esecuzione
$isTestEnvironment = strpos($_SERVER['SCRIPT_NAME'], 'test_integration.php') !== false ||
                     strpos($_SERVER['HTTP_REFERER'] ?? '', 'test_') !== false ||
                     strpos($_SERVER['REQUEST_URI'] ?? '', 'test_') !== false;

if (!isset($_SESSION['user_id']) && !$isTestEnvironment) {
    http_response_code(401);
    exit('Non autorizzato');
}

// Carica configurazione
$config = require_once 'includes/config.php';
?>

<!-- Modal Container -->
<div id="inertialMassModal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h2>Calcolo Massa Inerziale Sismica</h2>
            <button type="button" class="modal-close" onclick="closeInertialMassModal()">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        
        <div class="modal-body">
            <form id="inertialMassForm">
                <!-- Sezione dati automatici da ASDP -->
                <div class="form-section">
                    <h3>Dati Sismici (da ASDP)</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Coordinate:</label>
                            <span id="im-coordinates">-</span>
                        </div>
                        <div class="info-item">
                            <label>Zona Sismica:</label>
                            <span id="im-seismic-zone">-</span>
                        </div>
                        <div class="info-item">
                            <label>ag:</label>
                            <span id="im-ag">-</span>
                        </div>
                        <div class="info-item">
                            <label>F0:</label>
                            <span id="im-f0">-</span>
                        </div>
                        <div class="info-item">
                            <label>TC*:</label>
                            <span id="im-tc">-</span>
                        </div>
                        <div class="info-item">
                            <label>Categoria Suolo:</label>
                            <span id="im-soil-category">-</span>
                        </div>
                        <div class="info-item">
                            <label>Smorz. Viscoso Eq. (η):</label>
                            <span id="im-damping">-</span>
                        </div>
                        <div class="info-item">
                            <label>Fattore di Struttura (q0):</label>
                            <span id="im-q0-factor">-</span>
                        </div>
                    </div>
                </div>
                
                <!-- Sezione dati da richiedere all'utente -->
                <div class="form-section">
                    <h3>Caratteristiche Strutturali</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="im-construction-category">Tipologia Costruttiva *</label>
                            <select id="im-construction-category" name="construction_category" required>
                                <option value="">Seleziona tipologia...</option>
                                <option value="bridge">Ponte/Viadotto</option>
                                <option value="building">Edificio</option>
                                <option value="prefab_building">Edificio Prefabbricato</option>
                            </select>
                        </div>

                        <div class="form-group" id="structure-subcategory" style="display: none;">
                            <label for="im-structure-type">Tipologia Strutturale *</label>
                            <select id="im-structure-type" name="structure_type">
                                <option value="">Seleziona...</option>
                                <!-- Popolato dinamicamente via JavaScript -->
                            </select>
                        </div>

                        <div class="form-group" id="slab-subcategory" style="display: none;">
                            <label for="im-slab-type">Tipologia Solaio/Impalcato *</label>
                            <select id="im-slab-type" name="slab_type">
                                <option value="">Seleziona...</option>
                                <!-- Popolato dinamicamente via JavaScript -->
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="im-construction-year">Anno di Costruzione *</label>
                            <input type="number" id="im-construction-year" name="construction_year"
                                   min="1900" max="2025" required
                                   placeholder="es. 2010">
                        </div>
                    </div>
                </div>
                
                <!-- Sezione piani -->
                <div class="form-section">
                    <div class="section-header">
                        <h3>Piani dell'Edificio</h3>
                        <button type="button" class="btn btn-primary" onclick="addFloor()">
                            + Aggiungi Piano
                        </button>
                    </div>
                    
                    <div id="floors-container">
                        <!-- I piani verranno aggiunti dinamicamente via JS -->
                    </div>
                </div>
                
                <!-- Pulsanti azione -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="closeInertialMassModal()">
                        Annulla
                    </button>
                    <button type="submit" class="btn btn-primary" id="calculateBtn">
                        <span class="btn-text">Calcola con AI</span>
                        <span class="btn-loader" style="display: none;">
                            <svg class="spinner" width="20" height="20" viewBox="0 0 50 50">
                                <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
                            </svg>
                        </span>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Sezione risultati (inizialmente nascosta) -->
        <div id="results-section" class="modal-results" style="display: none;">
            <h3>Risultati del Calcolo</h3>
            <div id="results-content">
                <!-- I risultati verranno inseriti qui -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="resetCalculation()">
                    Nuovo Calcolo
                </button>
                <button type="button" class="btn btn-primary" onclick="saveResults()">
                    Salva Risultati
                </button>
            </div>
        </div>
    </div>
</div>
